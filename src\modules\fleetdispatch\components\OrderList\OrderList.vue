<template>
    <div class="order-list-container">
        <div class="header">
            <!-- 隐藏标题，因为已经在FlexibleLayout中有了 -->
            <!-- <h3>订单列表 ({{ filteredOrders.length }})</h3> -->
            <div class="all-controls-row">
                <el-input v-model="searchQuery" placeholder="搜索任意" clearable prefix-icon="Search" class="search-input" />
                <el-date-picker v-model="selectedDate" type="date" placeholder="选择日期" :default-value="new Date()" format="YYYY-MM-DD" value-format="YYYY-MM-DD" class="date-picker" @change="handleDateChange" />
                <el-select
                    v-model="selectedShift"
                    placeholder="选择班次"
                    class="shift-select"
                    @change="handleShiftChange"
                    value-key="id"
                 >
                    <el-option
                        v-for="shift in shifts"
                        :key="shift.id"
                        :label="shift.label"
                        :value="shift"
                    />
                 </el-select>
                <span class="order-count">({{ filteredOrders.length }})</span>
                <el-switch v-model="showAllOrders" active-text="全部" inactive-text="待分配" inline-prompt />
                <el-switch v-model="hideClosedExceptionalOrders" active-text="隐藏关闭/异常" inactive-text="显示全部" inline-prompt style="margin-left: 10px;" />
                <template v-if="orderStore.selectedOrders.length">
                    <span class="selected-count">已选择 {{ orderStore.selectedOrders.length }} 个订单</span>
                    <el-dropdown @command="handleAssignCommand" :disabled="!selectedDriver">
                        <el-button type="primary" size="small" :disabled="!selectedDriver">
                            分配
                            <el-icon class="el-icon--right"><arrow-down /></el-icon>
                        </el-button>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item command="assign">分配订单</el-dropdown-item>
                                <el-dropdown-item command="assignAndOptimize">分配并优化路线</el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                    <el-button type="warning" size="small" :disabled="!orderStore.hasAssignedOrders" @click="unassignOrders">取消分配</el-button>
                    <el-button type="danger" size="small" icon="Close" @click="clearAllSelections">清除选择</el-button>
                </template>
                <!-- <el-button type="success" size="small" icon="Download" @click="backupOrders" :loading="isBackingUp">备份订单</el-button> -->
            </div>
        </div>

        <div ref="tableContainer" class="orders-wrapper">
            <el-empty v-if="!filteredOrders.length" :description="emptyText" />

            <div
                v-else
                class="virtual-table-wrapper"
                :style="virtualScrollStyle"
            >
                <div
                    class="virtual-table-content"
                    :style="virtualScrollContentStyle"
                >
                    <el-table
                        ref="tableRef"
                        :data="dynamicTableData"
                        style="width: 100%"
                        :row-class-name="getRowClass"
                        class="order-table"
                        table-layout="fixed"
                        :height="isVirtualScrollEnabled ? null : tableHeight"
                        size="small"
                        border
                        @row-click="selectOrder"
                    >
                        <el-table-column width="28" fixed="left">
                            <template #header>
                                <el-checkbox
                                    :model-value="isAllSelected"
                                    :indeterminate="isIndeterminate"
                                    @change="handleSelectAll"
                                />
                            </template>
                            <template #default="{ row }">
                                <el-checkbox
                                    v-if="row.dispatchStatus === 'pending' || !row.dispatchStatus"
                                    v-model="row.isSelected"
                                    class="order-checkbox"
                                    @change="(val) => handleOrderSelect(row.id)"
                                    @click.stop
                                />
                            </template>
                        </el-table-column>

                        <el-table-column prop="no" label="订单号" width="90" fixed="left">
                            <template #default="{ row }">
                                <el-tooltip :content="row.no" placement="top">
                                    <span class="order-no">{{ row.no }}</span>
                                </el-tooltip>
                            </template>
                        </el-table-column>

                        <el-table-column label="类型" width="50" align="center">
                            <template #default="{ row }">
                                <el-tag
                                    size="small"
                                    :type="row.type === 'PICKUP' ? 'warning' : 'success'"
                                >
                                    {{ row.type === 'PICKUP' ? '取' : '派' }}
                                </el-tag>
                            </template>
                        </el-table-column>

                        <el-table-column label="状态" width="80" align="center">
                            <template #default="{ row }">
                                <el-tag
                                    size="small"
                                    :type="getStatusTagType(row.status)"
                                >
                                    {{ getStatusText(row.status) }}
                                </el-tag>
                            </template>
                        </el-table-column>

                        <el-table-column prop="name" label="联系人" width="70">
                            <template #default="{ row }">
                                <span>{{ row.name || '无' }}</span>
                            </template>
                        </el-table-column>

                        <el-table-column prop="phone" label="电话" width="90">
                            <template #default="{ row }">
                                <el-tooltip :content="row.phone || '无'" placement="top">
                                    <span>{{ row.phone || '无' }}</span>
                                </el-tooltip>
                            </template>
                        </el-table-column>

                        <el-table-column prop="address" label="地址" min-width="120">
                            <template #default="{ row }">
                                <el-tooltip
                                    :content="row.address || '无'"
                                    placement="top"
                                    :show-after="500"
                                >
                                    <span class="address-cell">{{ row.address || '无' }}</span>
                                </el-tooltip>
                            </template>
                        </el-table-column>

                        <el-table-column prop="remark" label="备注" width="70">
                            <template #default="{ row }">
                                <el-tooltip
                                    :content="row.remark"
                                    placement="top"
                                    :show-after="500"
                                >
                                    <span class="remark-cell">{{ row.remark || '-' }}</span>
                                </el-tooltip>
                            </template>
                        </el-table-column>

                        <el-table-column label="线路" width="90" fixed="right">
                            <template #default="{ row }">
                                <template v-if="row.route_number">
                                    <!-- 显示司机名字替代路线编号 -->
                                    <el-tooltip
                                        :content="`路线: ${formatRouteNumber(row.route_number)} ${getDriverNameByRouteId(row.route_number) ? '- 司机: ' + getDriverNameByRouteId(row.route_number) : ''}`"
                                        placement="top"
                                    >
                                        <span class="route-name">{{ getDriverNameByRouteId(row.route_number) || formatRouteNumber(row.route_number) }}</span>
                                    </el-tooltip>
                                    <span v-if="row.stop_no" class="stop-number">
                                        {{ row.stop_no }}
                                    </span>
                                </template>
                                <template v-else-if="row.driver_id">
                                    <el-tooltip :content="getDriverNameById(row.driver_id)" placement="top">
                                        <span class="driver-name">{{ getDriverNameById(row.driver_id) }}</span>
                                    </el-tooltip>
                                    <span v-if="row.stop_no" class="stop-number">
                                        {{ row.stop_no }}
                                    </span>
                                </template>
                                <template v-else>
                                    <span>未分配</span>
                                </template>
                            </template>
                        </el-table-column>

                        <el-table-column type="expand" width="30" fixed="right">
                            <template #default="props">
                                <div class="order-detail">
                                    <div class="order-detail-item"><strong>订单号:</strong> {{ props.row.no || '无' }}</div>
                                    <div class="order-detail-item"><strong>联系人:</strong> {{ props.row.name || '无' }}</div>
                                    <div class="order-detail-item"><strong>公司:</strong> {{ props.row.company || '无' }}</div>
                                    <div class="order-detail-item"><strong>电话:</strong> {{ props.row.phone || '无' }}</div>
                                    <div class="order-detail-item"><strong>地址:</strong> {{ props.row.address || '无' }}</div>
                                    <div class="order-detail-item"><strong>备注:</strong> {{ props.row.remark || '无' }}</div>
                                    <div class="order-detail-item">
                                        <strong>对方信息:</strong>
                                        {{ props.row.counterpart?.name || '无' }}
                                        {{ props.row.counterpart?.phone ? `(${props.row.counterpart.phone})` : '' }}
                                        {{ props.row.counterpart?.address ? `- ${props.row.counterpart.address}` : '' }}
                                    </div>
                                    <!-- <div class="order-detail-item"><strong>下单时间:</strong> {{ formatDateTime(props.row.created_at) || '未知' }}</div> -->
                                    <div class="order-detail-item"><strong>预计送达:</strong> {{ formatDateTime(props.row.eta) || '未知' }}</div>
                                    <div class="order-detail-item"><strong>状态:</strong> {{ getStatusText(props.row.status) || '未知' }}</div>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, onUnmounted, nextTick } from 'vue'
import { storeToRefs } from 'pinia'
import { useOrderStore } from '../../stores/order'
import { useDriverStore } from '../../stores/driver'
import { useTimeStore } from '../../stores/time'
import { useRouteStore } from '../../stores/route'
import { useAddressStore } from '../../stores/address'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs' // 确保已安装 dayjs
import { eventBus, EVENT_TYPES } from '../../utils/eventBus'
import { useVirtualScroll } from '../../composables/useVirtualScroll'
import { performanceMonitor } from '../../utils/performanceMonitor'
import { orderAPI } from '../../api'
import { ArrowDown } from '@element-plus/icons-vue'

// 引入 lodash 的防抖和节流函数
import { debounce, throttle } from 'lodash-es'

const orderStore = useOrderStore()
const driverStore = useDriverStore()
const routeStore = useRouteStore()
const timeStore = useTimeStore()
const addressStore = useAddressStore()

// 确保地址存储已初始化
addressStore.initialize()

// Firebase消息处理函数
const handleFirebaseMessage = async message => { // async 可能不再需要
    console.log('OrderList组件收到Firebase消息:', message);
    console.log('Firebase消息详细数据:', JSON.stringify(message));

    // 根据消息类型进行不同处理
    // OrderList 主要通过监听 store 变化来更新，不再需要直接响应原始消息来 fetch 数据
    // 移除针对 ORDER_CREATE, ORDER_UPDATE, new_order 的 fetchOrders 调用
    if (message.action === 'ORDER_CREATE' || message.action === 'ORDER_CREATED') {
        console.log('OrderList 收到新订单创建消息 (仅记录，不fetch)');
        // await fetchOrders(); // <-- 移除
        // ordersCache.key = ''; // <-- 移除 (让 watch 或 computed 触发)
        // createOrdersCache(); // <-- 移除
    } else if (message.action === 'ORDER_UPDATE') {
        console.log('OrderList 收到订单更新消息 (仅记录，不fetch)');
        // await fetchOrders(); // <-- 移除
        // ordersCache.key = ''; // <-- 移除
        // createOrdersCache(); // <-- 移除
    } else if (message.action === 'ORDER_STATUS_UPDATED') {
        console.log('OrderList 收到订单状态更新消息 (仅记录)');
        // 之前的逻辑尝试在这里做增量更新，但现在 store 会处理更新
        // 我们依赖 store 的变化来更新列表
        // 可以考虑在这里添加一个小的视觉提示，比如短暂高亮变化的行？(可选)
    } else if (message.type === 'new_order') { // 旧格式
        console.log('OrderList 收到新订单消息(旧格式) (仅记录，不fetch)');
        // await fetchOrders(); // <-- 移除
        // ordersCache.key = ''; // <-- 移除
        // createOrdersCache(); // <-- 移除
    } else if (message.action === 'DRIVER_UPDATE' || message.action === 'DRIVER_POSITIONS') {
        // console.log('OrderList 收到司机位置更新 (通常不需要处理)');
        // 司机位置通常只影响地图
    } else {
        console.warn('OrderList 未显式处理的消息类型:', message.action || message.type);
    }
};

// 处理Firebase权限问题
const handleFirebasePermission = () => {
    console.log('尝试处理Firebase通知权限问题')

    try {
        // 检查Notification API是否可用
        if (!('Notification' in window)) {
            console.log('此浏览器不支持桌面通知')
            return false
        }

        // 检查当前权限状态
        if (Notification.permission === 'denied') {
            console.log('通知已被用户或浏览器明确拒绝')
            ElMessage.warning({
                message: '推送通知权限已被拒绝，将无法接收实时消息。请在浏览器设置中允许通知。',
                duration: 5000
            })
            return false
        }

        // 如果当前权限是default，尝试请求权限
        if (Notification.permission === 'default') {
            console.log('尝试请求通知权限')
            Notification.requestPermission().then(permission => {
                if (permission === 'granted') {
                    console.log('用户授予了通知权限')
                    ElMessage.success('成功获取通知权限，现在可以接收消息推送')
                    return true
                } else {
                    console.log('用户拒绝了通知权限', permission)
                    ElMessage.warning('未获取通知权限，将无法接收实时消息更新')
                    return false
                }
            }).catch(error => {
                console.error('请求通知权限时出错:', error)
                return false
            })
        }

        // 已经有权限
        if (Notification.permission === 'granted') {
            console.log('已有通知权限')
            return true
        }
    } catch (error) {
        console.error('处理Firebase权限时发生错误:', error)
        return false
    }
}

// 解构需要的响应式引用
const { selectedOrder } = storeToRefs(orderStore)
const { selectedDriver } = storeToRefs(driverStore)
const { selectedDate, selectedShift, shifts } = storeToRefs(timeStore)

// 表格高度
const tableHeight = computed(() => {
    // 预留顶部过滤栏和其他元素的高度，加上边距
    return 'calc(100vh - var(--g-header-height) - 80px)'
})

// 使用 showAllOrders 替代 viewMode
const showAllOrders = ref(false) // 默认为"未分配"视图

// 添加隐藏关闭/异常订单的开关，默认开启
const hideClosedExceptionalOrders = ref(true)

// 添加搜索相关的代码
const searchQuery = ref('')

// 添加选中司机ID的状态
const selectedDriverIds = ref([])

// 添加加载状态变量
const isLoading = ref(false)

// 使用 isHandlingMapEvent 替代 isHandlingListEvent，使命名更符合组件角色
const isHandlingMapEvent = ref(false)

// 添加订单统计计数变量
const totalOrderCount = ref(0)
const scheduledCount = ref(0)
const unscheduledCount = ref(0)
const filteredCount = ref(0)

// 更新当前页数据函数
const updateCurrentPageData = () => {
    // 简单的空函数，用于修复错误
    // 这个函数在OrderList.vue中被调用，但没有实际定义
    console.log('更新当前页数据')
}

// 防抖处理的事件发送函数
const debouncedEmitOrdersFiltered = debounce((newOrders, filterCriteria) => {
    // 只有在锁定期结束后才发送事件
    if (!isHandlingMapEvent.value) {
        eventBus.emit(EVENT_TYPES.ORDERS_FILTERED, {
            orders: newOrders,
            filterCriteria,
            hash: createOrdersHash(newOrders)
        })
    }

    // 延迟释放事件处理锁
    setTimeout(() => {
        isHandlingMapEvent.value = false
    }, 500) // 增加到500ms
}, 500) // 增加防抖时间到500ms

// 防抖处理的订单选择事件发送
const debouncedEmitOrderSelected = debounce(order => {
    // 只有在锁定期结束后才发送事件
    if (!isHandlingMapEvent.value) {
        eventBus.emit(EVENT_TYPES.ORDER_SELECTED, {
            orderId: order.id,
            isSelected: order.isSelected,
            order: order
        })
    }
}, 300) // 增加防抖时间到300ms

// 处理日期变化 - 防抖处理
const handleDateChange = debounce(async date => {
    console.log('日期变更:', date)

    // 标记开始加载
    const loadingInstance = ElMessage({
        message: '加载中...',
        type: 'info',
        duration: 0
    })

    try {
        // 设置选择的日期
        timeStore.setDate(date)

        // 获取新日期的订单
        await orderStore.fetchOrders()

        // 清除所有选择
        orderStore.clearSelection()

        // 更新路由数据
        try {
            await routeStore.fetchRoutes({
                date: timeStore.selectedDate,
                // priority: timeStore.selectedShift.value
                shiftId: timeStore.selectedShift?.id // 使用 shiftId
            })
            console.log('路线数据已更新:', routeStore.routes.length)
        } catch (error) {
            console.error('更新路线数据失败:', error)
        }

        // 强制清除缓存，重新计算过滤结果
        ordersCache.key = ''
        createOrdersCache()

        // 强制刷新表格
        nextTick(() => {
            if (tableRef.value) {
                tableRef.value.doLayout()
            }

            // 同步地图显示 - 直接发送事件，不使用防抖
            eventBus.emit(EVENT_TYPES.ORDERS_FILTERED, {
                orders: filteredOrders.value,
                filterCriteria: {
                    date: timeStore.selectedDate,
                    // shift: timeStore.selectedShift,
                    shiftId: timeStore.selectedShift?.id, // 使用 shiftId
                    isDateChange: true,
                    showAllOrders: showAllOrders.value, // 添加当前显示模式
                    hideClosedExceptionalOrders: hideClosedExceptionalOrders.value, // 添加隐藏关闭/异常订单模式
                    timestamp: Date.now()
                }
            })

            // 关闭加载提示
            loadingInstance.close()
            ElMessage.success('日期已更新')
        })
    } catch (error) {
        console.error('日期切换失败:', error)
        loadingInstance.close()
        ElMessage.error('加载失败，请重试')
    }
}, 300)

// 处理班次变化 - 防抖处理
const handleShiftChange = debounce(async shift => {
    console.log('班次变更:', shift)

    // 标记开始加载
    const loadingInstance = ElMessage({
        message: '加载中...',
        type: 'info',
        duration: 0
    })

    try {
        // 设置选择的班次
        timeStore.setShift(shift)

        // 获取新班次的订单
        await orderStore.fetchOrders()

        // 清除所有选择
        orderStore.clearSelection()

        // 更新路由数据
        try {
            await routeStore.fetchRoutes({
                date: timeStore.selectedDate,
                shift: shift // 传递完整的 shift 对象
            })
            console.log('路线数据已更新:', routeStore.routes.length)

            // 更新司机数据
            console.log('更新司机数据，使用当前班次...')
            await driverStore.fetchDrivers({
                date: timeStore.selectedDate,
                shift: shift // 传递完整的 shift 对象
            })
            console.log('司机数据已更新:', driverStore.drivers.length)
        } catch (error) {
            console.error('更新路线或司机数据失败:', error)
        }

        // 强制清除缓存，重新计算过滤结果
        ordersCache.key = ''
        createOrdersCache()

        // 强制刷新表格
        nextTick(() => {
            if (tableRef.value) {
                tableRef.value.doLayout()
            }

            // 同步地图显示 - 直接发送事件，不使用防抖
            eventBus.emit(EVENT_TYPES.ORDERS_FILTERED, {
                orders: filteredOrders.value,
                filterCriteria: {
                    date: timeStore.selectedDate,
                    // shift: timeStore.selectedShift,
                    shiftId: timeStore.selectedShift?.id, // 使用 shiftId
                    isShiftChange: true,
                    showAllOrders: showAllOrders.value, // 添加当前显示模式
                    hideClosedExceptionalOrders: hideClosedExceptionalOrders.value, // 添加隐藏关闭/异常订单模式
                    timestamp: Date.now()
                }
            })

            // 关闭加载提示
            loadingInstance.close()
            ElMessage.success('班次已更新')
        })
    } catch (error) {
        console.error('班次切换失败:', error)
        loadingInstance.close()
        ElMessage.error('加载失败，请重试')
    }
}, 300)

// 获取订单数据
const fetchOrders = async() => {
    let retryCount = 0;
    const maxRetries = 3;
    const retryDelay = 1000; // 1秒延迟

    // 显示加载提示，但保留原先的 isLoading 状态
    let loadingMessage = null;

    const attemptFetch = async() => {
        try {
            if (retryCount === 0) {
                // 第一次尝试时显示加载提示
                isLoading.value = true;
                loadingMessage = ElMessage({
                    message: '正在加载订单数据...',
                    type: 'info',
                    duration: 0
                });
            } else {
                console.log(`第 ${retryCount} 次重试获取订单数据...`);
            }

            await orderStore.fetchOrders();

            console.log('获取订单完成，全部:', orderStore.allOrders.length,
                      '未分配:', orderStore.orders.length,
                      '显示模式:', window.showAllOrders ? '全部' : '仅未分配');

            // 强制清除缓存并重新计算过滤结果
            ordersCache.key = '';

            // 根据当前显示模式设置正确的订单集合
            if (!showAllOrders.value) {
                console.log('设置过滤后的订单为未分配订单')
                // 如果需要隐藏关闭/异常订单，过滤掉它们
                if (hideClosedExceptionalOrders.value) {
                    console.log('隐藏关闭/异常订单模式，过滤掉未分配的关闭/异常订单')
                    window.filteredOrdersFromList = orderStore.orders.filter(order =>
                        !(order.status === 'closed' || order.status === 'exceptional')
                    )
                } else {
                    window.filteredOrdersFromList = orderStore.orders
                }
            } else {
                console.log('设置过滤后的订单为所有订单')
                // 如果需要隐藏关闭/异常订单，过滤掉未分配的关闭/异常订单
                if (hideClosedExceptionalOrders.value) {
                    console.log('隐藏关闭/异常订单模式，过滤掉未分配的关闭/异常订单')
                    window.filteredOrdersFromList = orderStore.allOrders.filter(order =>
                        order.driver_id || !(order.status === 'closed' || order.status === 'exceptional')
                    )
                } else {
                    window.filteredOrdersFromList = orderStore.allOrders
                }
            }

            // 重新计算过滤结果
            createOrdersCache();

            // 成功后关闭加载提示
            if (loadingMessage) {
                loadingMessage.close();
            }
            isLoading.value = false;

            return true; // 表示成功
    } catch (error) {
            console.error(`获取订单失败 (尝试 ${retryCount + 1}/${maxRetries}):`, error);

            if (retryCount < maxRetries - 1) {
                // 还有重试次数，等待后重试
                retryCount++;
                await new Promise(resolve => setTimeout(resolve, retryDelay));
                return attemptFetch(); // 递归重试
            } else {
                // 所有重试都失败了
                console.error('所有重试都失败，无法获取订单数据');

                // 关闭之前的加载提示
                if (loadingMessage) {
                    loadingMessage.close();
                }

                isLoading.value = false;
                ElMessage.error('获取订单数据失败，请稍后重试');
                return false; // 表示失败
    }
        }
    };

    return attemptFetch();
}

// 优化的选择状态初始化
const initializeOrderSelection = () => {
    // 创建已选订单的 Set 用于快速查找
    if (!orderStore.selectedOrdersSet) {
        orderStore.selectedOrdersSet = new Set(orderStore.selectedOrders.map(o => o.id))
    }

    // 批量初始化isSelected属性，避免频繁修改
    const selectedIds = orderStore.selectedOrdersSet
    const allOrders = orderStore.allOrders

    // 创建一个批处理函数，每次处理一部分订单，避免阻塞主线程
    const batchSize = 100
    const processBatch = startIndex => {
        const endIndex = Math.min(startIndex + batchSize, allOrders.length)

        for (let i = startIndex; i < endIndex; i++) {
            const order = allOrders[i]
            order.isSelected = selectedIds.has(order.id)
        }

        if (endIndex < allOrders.length) {
            // 使用requestAnimationFrame或setTimeout延迟处理下一批
            setTimeout(() => {
                processBatch(endIndex)
            }, 0)
        } else {
            console.log('完成订单选择状态初始化，总订单数:', allOrders.length)
        }
    }

    // 开始处理第一批
    processBatch(0)
}

// 组件挂载时初始化数据
onMounted(async() => {
    // 设置全局显示模式，供地图组件使用
    window.showAllOrders = showAllOrders.value
    window.hideClosedExceptionalOrders = hideClosedExceptionalOrders.value
    console.log('初始化显示模式:', window.showAllOrders ? '全部订单' : '仅显示未分配订单')
    console.log('初始化隐藏关闭/异常订单模式:', window.hideClosedExceptionalOrders ? '隐藏' : '显示')

    // 确保初始状态下使用正确的订单集合
    if (!showAllOrders.value) {
        console.log('初始状态为"未分配"视图，确保使用未分配订单')
        // 如果需要隐藏关闭/异常订单，过滤掉它们
        if (hideClosedExceptionalOrders.value) {
            console.log('初始状态为"隐藏关闭/异常订单"，过滤掉未分配的关闭/异常订单')
            window.filteredOrdersFromList = orderStore.orders.filter(order =>
                !(order.status === 'closed' || order.status === 'exceptional')
            )
        } else {
            window.filteredOrdersFromList = orderStore.orders
        }
    } else {
        console.log('初始状态为"全部"视图，确保使用所有订单')
        // 如果需要隐藏关闭/异常订单，过滤掉未分配的关闭/异常订单
        if (hideClosedExceptionalOrders.value) {
            console.log('初始状态为"隐藏关闭/异常订单"，过滤掉未分配的关闭/异常订单')
            window.filteredOrdersFromList = orderStore.allOrders.filter(order =>
                order.driver_id || !(order.status === 'closed' || order.status === 'exceptional')
            )
        } else {
            window.filteredOrdersFromList = orderStore.allOrders
        }
    }

    try {
        // 检查并处理Firebase通知权限
        handleFirebasePermission()

        // 先获取班次数据
        await timeStore.fetchShifts() // <--- 在获取订单之前获取班次数据
        console.log('班次数据获取完成，当前班次:', timeStore.selectedShift ? timeStore.selectedShift.label : '未设置')

        // 然后获取司机数据，传递 shift 参数
        console.log('获取司机数据，使用当前班次...')
        await driverStore.fetchDrivers({
            date: timeStore.selectedDate,
            shift: timeStore.selectedShift
        })
        console.log('司机数据获取完成，数量:', driverStore.drivers.length)

        // 获取订单数据并处理结果
        const ordersLoaded = await fetchOrders()

        // 只有在成功获取订单后才执行后续步骤
        if (ordersLoaded) {
        // 初始化所有订单的isSelected属性
        initializeOrderSelection()

        // 订阅地图点击订单事件
        eventBus.on(EVENT_TYPES.MAP_ORDER_CLICKED, handleMapOrderClicked)

        // 订阅司机选择变化事件
        eventBus.on(EVENT_TYPES.DRIVER_SELECTION_CHANGED, data => {
            selectedDriverIds.value = data.selectedDriverIds || []
            console.log('订单列表收到选中司机IDs:', selectedDriverIds.value)

            // 立即强制更新过滤结果并通知地图
            ordersCache.key = '' // 清除缓存
            const filteredResults = createOrdersCache() // 重新计算过滤结果

            // 立即发送过滤事件，不使用防抖
            eventBus.emit(EVENT_TYPES.ORDERS_FILTERED, {
                orders: filteredResults,
                filterCriteria: {
                    searchQuery: searchQuery.value,
                    selectedDriverIds: selectedDriverIds.value,
                    selectedDriver: driverStore.selectedDriver?.id,
                    selectedRouteId: routeStore.selectedRoute?.id,
                    showAllOrders: showAllOrders.value,
                    hideClosedExceptionalOrders: hideClosedExceptionalOrders.value,
                    date: timeStore.selectedDate,
                    // shift: timeStore.selectedShift
                    shiftId: timeStore.selectedShift?.id // 使用 shiftId
                }
            })
        })

        // 注册Firebase消息监听
        try {
            eventBus.on(EVENT_TYPES.FIREBASE_MESSAGE_RECEIVED, handleFirebaseMessage)
            console.log('OrderList组件已注册Firebase消息监听器')
        } catch (error) {
            console.error('注册Firebase消息监听器失败:', error)
        }

        // 订阅地图更新事件
        eventBus.on(EVENT_TYPES.MAP_UPDATING, handleMapUpdating)

        // 订阅订单更新事件
        eventBus.on(EVENT_TYPES.ORDERS_UPDATED, data => {
            console.log('订单列表收到订单更新事件:', data)

            // 特殊处理时间范围变化导致的更新
            if (data && data.isTimeRangeChange) {
                console.log('检测到时间范围变化导致的订单更新, 强制刷新界面')

                // 强制清除缓存
                ordersCache.key = ''
                createOrdersCache() // 重新计算过滤结果

                // 强制刷新表格
                nextTick(() => {
                    if (tableRef.value) {
                        tableRef.value.doLayout()
                        console.log('时间范围变化：表格已强制刷新')
                    }

                    // 发送事件通知地图组件更新
                    eventBus.emit(EVENT_TYPES.ORDERS_FILTERED, {
                        orders: filteredOrders.value,
                        filterCriteria: {
                            searchQuery: searchQuery.value,
                            selectedDriverIds: selectedDriverIds.value,
                            showAllOrders: showAllOrders.value,
                            hideClosedExceptionalOrders: hideClosedExceptionalOrders.value,
                            date: timeStore.selectedDate,
                            shift: timeStore.selectedShift, // 传递完整的 shift 对象
                            isTimeRangeChange: true,
                            timestamp: Date.now()
                        }
                    })
                })

                return // 已处理完毕，不需要继续执行
            }

            // 如果有详细的更新数据
            if (data && data.orders && Array.isArray(data.orders)) {
                // 立即更新本地数据模型
                data.orders.forEach(updatedOrder => {
                    const localOrder = orderStore.allOrders.find(o => o.id === updatedOrder.id)
                    if (localOrder) {
                        // 更新停靠点序号
                        localOrder.stop_no = updatedOrder.stop_no

                        // 确保其他属性也同步
                        if (updatedOrder.route_id) {
                            localOrder.route_id = updatedOrder.route_id
                        }
                        if (updatedOrder.route_number) {
                            localOrder.route_number = updatedOrder.route_number
                        }
                    }
                })

                // 立即强制表格更新
                nextTick(() => {
                    // 清除缓存，强制重新计算过滤结果
                    ordersCache.key = ''
                    const updatedResults = createOrdersCache()

                    // 强制更新表格显示
                    if (tableRef.value) {
                        tableRef.value.doLayout()
                        console.log('订单表格已强制刷新，显示最新的停靠点序号')
                    } else {
                        console.log('订单数据已更新，表格刷新完成')
                    }
                })
            } else {
                // 兼容旧的调用方式，只清除缓存
                console.log('收到订单更新事件，重新计算过滤结果')
                ordersCache.key = ''
                createOrdersCache()
            }
        })

        // 订阅订单列表更新事件 - 用于处理新订单通知
        eventBus.on(EVENT_TYPES.ORDER_LIST_UPDATED, (data) => {
            console.log('订单列表收到列表更新事件，刷新显示', data)

            // 处理强制刷新事件
            if (data && data.isForceRefresh) {
                console.log('订单列表收到强制刷新事件，立即重新获取数据');

                // 强制清除缓存和状态
                ordersCache.key = '';

                // 强制刷新订单数据
                orderStore.invalidateCache();

                // 重新获取最新的订单数据
                const updatedResults = createOrdersCache();

                // 强制更新表格显示
                nextTick(() => {
                    if (tableRef.value) {
                        tableRef.value.doLayout();
                        console.log('订单表格已强制重新布局显示');
                    }

                    // 更新过滤器的当前状态显示
                    totalOrderCount.value = orderStore.allOrders.length;
                    scheduledCount.value = orderStore.assignedOrders.length;
                    unscheduledCount.value = orderStore.orders.length;
                    filteredCount.value = updatedResults.length;

                    // 强制更新当前页数据
                    updateCurrentPageData();

                    // 同步通知地图组件更新显示
                    eventBus.emit(EVENT_TYPES.ORDERS_FILTERED, {
                        orders: updatedResults,
                        filterCriteria: {
                            searchQuery: searchQuery.value,
                            selectedDriverIds: selectedDriverIds.value,
                            selectedDriver: driverStore.selectedDriver?.id,
                            selectedRouteId: routeStore.selectedRoute?.id,
                            showAllOrders: showAllOrders.value,
                            hideClosedExceptionalOrders: hideClosedExceptionalOrders.value,
                            date: timeStore.selectedDate,
                            shift: timeStore.selectedShift, // 传递完整的 shift 对象
                            isForceRefresh: true, // 标记这是强制刷新
                            timestamp: Date.now()
                        }
                    });
                });

                return; // 提前返回，不执行后面的普通更新逻辑
            }

            // 清除缓存，强制重新计算过滤结果
            ordersCache.key = '';
            const updatedResults = createOrdersCache();

            // 强制更新表格显示
            nextTick(() => {
                if (tableRef.value) {
                    tableRef.value.doLayout();
                    console.log('收到订单列表更新事件，表格已刷新');
                }

                // 同步通知地图组件更新显示
                eventBus.emit(EVENT_TYPES.ORDERS_FILTERED, {
                    orders: updatedResults,
                    filterCriteria: {
                        searchQuery: searchQuery.value,
                        selectedDriverIds: selectedDriverIds.value,
                        selectedDriver: driverStore.selectedDriver?.id,
                        selectedRouteId: routeStore.selectedRoute?.id,
                        showAllOrders: showAllOrders.value,
                        hideClosedExceptionalOrders: hideClosedExceptionalOrders.value,
                        date: timeStore.selectedDate,
                        shift: timeStore.selectedShift, // 传递完整的 shift 对象
                        isAssignmentChange: data?.isAssignmentChange || false, // 添加标记
                        timestamp: Date.now()
                    }
            });
                })
        })

        // 添加对批量选择事件的监听
        eventBus.on(EVENT_TYPES.ORDERS_BATCH_SELECTED, data => {
            console.log('订单列表收到批量选择事件:', data)
            if (isHandlingMapEvent.value) return

            try {
                isHandlingMapEvent.value = true

                // 从地图组件传来的已经包含了选中的订单ID数组
                const orderIds = data.orderIds || []

                if (orderIds.length > 0) {
                    // 立即更新表格，不使用防抖
                    setTimeout(() => {
                        // 强制表格刷新，确保显示选中状态
                        ElMessage.success(`已选择 ${orderIds.length} 个订单`)
                        nextTick(() => {
                            isHandlingMapEvent.value = false
                        })
                    }, 50)
                }
            } catch (error) {
                console.error('处理批量选择事件失败:', error)
                isHandlingMapEvent.value = false
            }
        })

        // 预热过滤订单缓存
        nextTick(() => {
            // 根据当前显示模式获取正确的订单集合
            let initialFilteredOrders;
            if (!showAllOrders.value) {
                console.log('初始化过滤结果：使用未分配订单')
                initialFilteredOrders = orderStore.orders;
            } else {
                console.log('初始化过滤结果：使用所有订单')
                initialFilteredOrders = orderStore.allOrders;
            }

            // 更新全局变量
            window.filteredOrdersFromList = initialFilteredOrders;

            // 更新缓存
            ordersCache.key = '';
            createOrdersCache();

            // 立即发送给地图组件，确保地图初始显示正确
            eventBus.emit(EVENT_TYPES.ORDERS_FILTERED, {
                orders: initialFilteredOrders,
                filterCriteria: {
                    searchQuery: searchQuery.value,
                    selectedDriverIds: selectedDriverIds.value,
                    selectedDriver: driverStore.selectedDriver?.id,
                    selectedRouteId: routeStore.selectedRoute?.id,
                    showAllOrders: showAllOrders.value,
                    date: timeStore.selectedDate,
                    shift: timeStore.selectedShift, // 传递完整的 shift 对象
                    isInitialLoad: true
                }
            })

            console.log('向地图发送初始订单列表，数量:', initialFilteredOrders.length)
        })

            if (isDevMode) {
                performanceMonitor.recordEvent('OrderList:mounted')
            }

            // 初始化后强制刷新一次过滤结果并广播，确保未分配订单正确显示
            nextTick(() => {
                // 强制清除缓存
                ordersCache.key = ''

                // 根据当前显示模式获取正确的订单集合
                let initialFilteredResults;
                if (!showAllOrders.value) {
                    console.log('最终初始化：使用未分配订单')
                    initialFilteredResults = orderStore.orders;
                } else {
                    console.log('最终初始化：使用所有订单')
                    initialFilteredResults = orderStore.allOrders;
                }

                // 更新全局变量
                window.filteredOrdersFromList = initialFilteredResults;

                // 更新缓存
                createOrdersCache();

                console.log('初始化完成，强制发送过滤事件，订单数量:', initialFilteredResults.length)

                // 发送过滤事件通知地图和其他组件
                eventBus.emit(EVENT_TYPES.ORDERS_FILTERED, {
                    orders: initialFilteredResults,
                    filterCriteria: {
                        searchQuery: searchQuery.value,
                        selectedDriverIds: selectedDriverIds.value,
                        selectedDriver: driverStore.selectedDriver?.id,
                        selectedRouteId: routeStore.selectedRoute?.id,
                        showAllOrders: showAllOrders.value,
                        hideClosedExceptionalOrders: hideClosedExceptionalOrders.value,
                        date: timeStore.selectedDate,
                        shift: timeStore.selectedShift, // 传递完整的 shift 对象
                        isInitialLoad: true
                    }
                })
            })
        } else {
            // 订单加载失败后的优雅处理 - 不显示错误提示，因为fetchOrders已经显示过了
            console.log('订单数据加载失败，尝试绑定最小化的事件处理');
            // 仍然订阅一些基本事件，允许用户尝试重新加载
            eventBus.on(EVENT_TYPES.ORDERS_UPDATED, () => {
                console.log('检测到订单更新事件，尝试重新加载');
                fetchOrders(); // 尝试重新获取数据
            });
        }

        if (isDevMode) {
            performanceMonitor.recordEvent('OrderList:mounted')
        }
    } catch (error) {
        console.error('初始化订单列表失败:', error)
        ElMessage.error('加载界面失败，请刷新页面重试')
    }
})

// 清理事件监听器
onUnmounted(() => {
    // 使用eventBus的off方法移除所有相关事件监听
    eventBus.off(EVENT_TYPES.MAP_ORDER_CLICKED)
    eventBus.off(EVENT_TYPES.DRIVER_SELECTION_CHANGED)
    eventBus.off(EVENT_TYPES.MAP_UPDATING)
    eventBus.off(EVENT_TYPES.ORDERS_UPDATED)
    eventBus.off(EVENT_TYPES.ORDERS_BATCH_SELECTED)
    eventBus.off(EVENT_TYPES.ORDER_LIST_UPDATED)

    // 移除Firebase消息监听
    eventBus.off(EVENT_TYPES.FIREBASE_MESSAGE_RECEIVED, handleFirebaseMessage)
    console.log('OrderList组件已移除Firebase消息监听器')

    // 取消所有待处理的防抖函数
    debouncedEmitOrdersFiltered.cancel()
    debouncedEmitOrderSelected.cancel()

    if (isDevMode) {
        performanceMonitor.recordEvent('OrderList:unmounted')
    }
})

// 订单过滤结果缓存
const ordersCache = {
    key: '',
    timestamp: 0,
    data: []
}

// 生成缓存键
const createCacheKey = () => {
    return JSON.stringify({
        showAll: showAllOrders.value,
        hideClosedExceptional: hideClosedExceptionalOrders.value,
        driverIds: selectedDriverIds.value,
        driverId: driverStore.selectedDriver?.id,
        routeId: routeStore.selectedRoute?.id,
        search: searchQuery.value,
        date: timeStore.selectedDate,
        // shift: selectedShift.value ? selectedShift.value.value : null
        shiftId: selectedShift.value?.id || null // 使用 selectedShift 的 id
    })
}

// 性能监控模式
const isDevMode = process.env.NODE_ENV === 'development'

// 监控订单过滤性能
function monitorOrderFiltering(fn) {
    return (...args) => {
        if (!isDevMode) return fn(...args)

        const id = performanceMonitor.startMeasure('orderFiltering')
        const result = fn(...args)
        const duration = performanceMonitor.endMeasure('orderFiltering', id)

        if (duration > 50) {
            console.warn(`订单过滤耗时: ${duration.toFixed(2)}ms`)
        }

        return result
    }
}

// 应用性能监控到关键函数
const createOrdersCache = monitorOrderFiltering(function() {
    const cacheKey = createCacheKey()

    // 如果缓存有效且未超时，直接返回
    if (ordersCache.key === cacheKey &&
        Date.now() - ordersCache.timestamp < 2000) {
        return ordersCache.data
    }

    // 使用引用，避免不必要的数组创建
    const baseOrders = showAllOrders.value ? orderStore.allOrders : orderStore.orders
    let orders = baseOrders
    let needsFiltering = false

    // 创建筛选条件值的快照，避免计算过程中值变化
    const driverIdsSnapshot = selectedDriverIds.value ? [...selectedDriverIds.value] : []
    const selectedDriverSnapshot = driverStore.selectedDriver?.id
    const selectedRouteSnapshot = routeStore.selectedRoute?.id
    const searchQuerySnapshot = searchQuery.value
    const hideClosedExceptionalSnapshot = hideClosedExceptionalOrders.value

    // 首先检查是否需要过滤
    needsFiltering = driverIdsSnapshot.length > 0 ||
        selectedDriverSnapshot ||
        selectedRouteSnapshot ||
        searchQuerySnapshot ||
        hideClosedExceptionalSnapshot

    // 只在需要过滤时才创建新数组
    if (needsFiltering) {
        // 使用Set进行高效的查找
        if (driverIdsSnapshot.length > 0) {
            const uniqueOrderIds = new Set()
            const uniqueOrders = []
            const driverIdSet = new Set(driverIdsSnapshot)

            // 获取选中司机的所有订单
            orderStore.assignedOrders.forEach(order => {
                if (order.driver_id && driverIdSet.has(order.driver_id) && !uniqueOrderIds.has(order.id)) {
                    uniqueOrders.push(order)
                    uniqueOrderIds.add(order.id)
                }
            })

            // 添加未分配订单（不包括重复的）
            orderStore.orders.forEach(order => {
                if (!uniqueOrderIds.has(order.id)) {
                    uniqueOrders.push(order)
                    uniqueOrderIds.add(order.id)
                }
            })

            orders = uniqueOrders
        }
        // 单个司机选择的情况 (兼容旧逻辑)
        else if (selectedDriverSnapshot) {
            // 合并司机订单和未分配订单
            const driverOrders = orderStore.getDriverOrders(selectedDriverSnapshot)
            const unassignedOrders = orderStore.orders

            // 使用Set避免重复
            const uniqueOrderIds = new Set(driverOrders.map(order => order.id))
            const combinedOrders = [...driverOrders]

            // 添加未分配订单
            unassignedOrders.forEach(order => {
                if (!uniqueOrderIds.has(order.id)) {
                    combinedOrders.push(order)
                }
            })

            orders = combinedOrders
        }

        // 根据选中的路线进一步筛选
        if (selectedRouteSnapshot) {
            orders = orderStore.getOrdersByRouteNumber(selectedRouteSnapshot)
        }

        // 搜索过滤 - 使用索引优化查找
        if (searchQuerySnapshot) {
            const query = searchQuerySnapshot.toLowerCase()

            // 优化搜索性能
            const matchesSearch = order => (
                (order.no && order.no.toLowerCase().includes(query)) ||
                (order.name && order.name.toLowerCase().includes(query)) ||
                (order.phone && order.phone.toLowerCase().includes(query)) ||
                (order.address && order.address.toLowerCase().includes(query)) ||
                (order.company && order.company.toLowerCase().includes(query))
            )

            orders = orders.filter(matchesSearch)
        }

        // 过滤未分配的 closed 和 exceptional 状态订单
        if (hideClosedExceptionalSnapshot) {
            orders = orders.filter(order => {
                // 如果订单已分配，保留
                if (order.driver_id) {
                    return true;
                }

                // 如果是未分配的 closed 或 exceptional 状态订单，过滤掉
                if (order.status === 'closed' || order.status === 'exceptional') {
                    return false;
                }

                // 其他情况保留
                return true;
            });
        }
    }

    // 更新缓存
    ordersCache.key = cacheKey
    ordersCache.timestamp = Date.now()
    ordersCache.data = orders

    return orders
})

// 优化的 filteredOrders 计算属性
const filteredOrders = computed(() => {
    // 计算过滤后的订单
    const results = createOrdersCache()

    // 每次过滤结果变化时，确保地图组件能够立即获得更新
    if (window.filteredOrdersFromList !== results && !isHandlingMapEvent.value) {
        // 异步发送事件，避免不必要的重复计算和循环依赖
        setTimeout(() => {
            // 立即更新全局变量
            window.filteredOrdersFromList = results

            // 发送事件给地图组件
            eventBus.emit(EVENT_TYPES.ORDERS_FILTERED, {
                orders: results,
                filterCriteria: {
                    searchQuery: searchQuery.value,
                    selectedDriverIds: selectedDriverIds.value,
                    selectedDriver: driverStore.selectedDriver?.id,
                    selectedRouteId: routeStore.selectedRoute?.id,
                    showAllOrders: showAllOrders.value,
                    hideClosedExceptionalOrders: hideClosedExceptionalOrders.value,
                    date: timeStore.selectedDate,
                    shift: timeStore.selectedShift // 传递完整的 shift 对象
                }
            })

            console.log('过滤条件变化，向地图发送更新，订单数量:', results.length)
        }, 0)
    }

    return results
})

// 基于内存使用优化的订单哈希函数
const createOrdersHash = orders => {
    if (!orders || !Array.isArray(orders) || orders.length === 0) {
        return '0-empty'
    }

    // 使用静态缓存加速哈希计算
    if (!createOrdersHash.cache) {
        createOrdersHash.cache = {
            lastOrders: null,
            lastHash: '',
            lastTs: 0
        }
    }

    // 检查时间缓存 - 500ms内不重复计算相同订单集合的哈希
    const now = Date.now()
    if (createOrdersHash.cache.lastOrders === orders &&
        now - createOrdersHash.cache.lastTs < 500) {
        return createOrdersHash.cache.lastHash
    }

    // 高效计算哈希 - 只使用长度和选中数量
    let orderCount = orders.length

    // 使用数组迭代而不是filter，减少一次遍历
    let selectedCount = 0
    for (let i = 0; i < orders.length; i++) {
        if (orders[i].isSelected) {
            selectedCount++
        }
    }

    // 添加时间戳避免频繁相同的哈希
    let hash = `o${orderCount}:s${selectedCount}:t${Math.floor(now / 1000)}`

    // 更新缓存
    createOrdersHash.cache.lastOrders = orders
    createOrdersHash.cache.lastHash = hash
    createOrdersHash.cache.lastTs = now

    return hash
}

// 记录上次发送的数据哈希
let lastSentOrdersHash = ''

// 优化的订单列表监听器，使用节流减少事件频率
const throttledWatchHandler = throttle(newOrders => {
    // 如果正在处理地图事件，跳过同步
    if (isHandlingMapEvent.value) {
        return
    }

    // 对订单进行浅拷贝，避免直接修改引用
    const ordersToProcess = [...newOrders]

    // 使用 requestAnimationFrame 在下一帧处理
    requestAnimationFrame(() => {
        // 计算当前订单数据的哈希值
        const currentHash = createOrdersHash(ordersToProcess)

        // 如果数据没有变化，跳过发送事件
        if (currentHash === lastSentOrdersHash) {
            return
        }

        // 更新数据哈希值
        lastSentOrdersHash = currentHash

        // 防止在发送事件后短时间内被地图事件触发
        isHandlingMapEvent.value = true

        // 创建过滤条件对象
        const filterCriteria = {
            searchQuery: searchQuery.value,
            selectedDriverIds: selectedDriverIds.value,
            selectedDriver: driverStore.selectedDriver?.id,
            selectedRouteId: routeStore.selectedRoute?.id,
            showAllOrders: showAllOrders.value,
            hideClosedExceptionalOrders: hideClosedExceptionalOrders.value,
            date: timeStore.selectedDate,
            shift: timeStore.selectedShift // 传递完整的 shift 对象
        }

        // 使用防抖函数发送事件
        debouncedEmitOrdersFiltered(ordersToProcess, filterCriteria)
    })
}, 500) // 增加节流时间到500ms

// 监听过滤订单的变化，并通知地图组件
watch(filteredOrders, throttledWatchHandler, { deep: false })

// 监听搜索查询变化，使用防抖减少计算频率
watch(searchQuery, debounce(() => {
    // 清除缓存，强制重新计算
    ordersCache.key = ''

    // 等待搜索输入完成后再设置处理标志
    if (!isHandlingMapEvent.value) {
        isHandlingMapEvent.value = true

        // 延迟释放标志
        setTimeout(() => {
            isHandlingMapEvent.value = false
        }, 500)
    }
}, 500)) // 增加防抖时间到500ms

// 更新空状态文本
const emptyText = computed(() => {
    if (selectedDriverIds.value && selectedDriverIds.value.length > 0) {
        return selectedDriverIds.value.length > 1
            ? `已选择 ${selectedDriverIds.value.length} 位司机，但暂无相关订单`
            : '所选司机暂无订单'
    } else if (driverStore.selectedDriver) {
        return `${driverStore.selectedDriver.name} 暂无订单`
    } else if (showAllOrders.value) {
        return '暂无订单'
    } else {
        return '暂无待分配订单'
    }
})

// 格式化路线编号
const formatRouteNumber = num => {
    return String(num).padStart(5, '0')
}

// 选择订单 - 优化版本
const selectOrder = order => {
    // 防止在事件处理过程中重复操作
    if (isHandlingMapEvent.value) return

    try {
        isHandlingMapEvent.value = true

        // 避免频繁状态切换，使用setTimeout确保UI有时间更新
        setTimeout(() => {
            if (selectedOrder.value?.id === order.id) {
                orderStore.clearSelectedOrder()
            } else {
                orderStore.selectOrder(order)
            }
        }, 10)

        // 延迟释放锁
        setTimeout(() => {
            isHandlingMapEvent.value = false
        }, 500) // 增加到500ms
    } catch (error) {
        console.error('选择订单失败:', error)
        isHandlingMapEvent.value = false
    }
}

// 处理来自地图的订单点击事件 - 优化版本
const handleMapOrderClicked = ({ orderId, isCtrlPressed }) => {
    if (isHandlingMapEvent.value) return

    try {
        isHandlingMapEvent.value = true

        // 在下一帧处理，避免阻塞当前帧
        requestAnimationFrame(() => {
            // 查找订单并更新UI状态
            const order = orderStore.allOrders.find(o => o.id === orderId)
            if (!order) {
                isHandlingMapEvent.value = false
                return
            }

            // 检查Ctrl键是否按下（多选）
            if (isCtrlPressed) {
                // 多选模式：切换选中状态
                orderStore.toggleOrderSelection(order)
            } else {
                // 单选模式
                const isCurrentlySelected = orderStore.selectedOrders.some(o => o.id === order.id)
                if (isCurrentlySelected) {
                    orderStore.toggleOrderSelection(order)
                } else {
                    orderStore.clearSelection()
                    orderStore.toggleOrderSelection(order)
                }
            }
        })
    } finally {
        // 释放锁
        setTimeout(() => {
            isHandlingMapEvent.value = false
        }, 500) // 增加到500ms
    }
}

// 处理地图更新事件 - 优化版本
const handleMapUpdating = throttle(() => {
    // 如果已经在暂停同步状态，跳过处理
    if (isHandlingMapEvent.value) {
        return
    }

    isHandlingMapEvent.value = true

    // 使用较长的延迟时间重置标志，减少重置频率
    setTimeout(() => {
        isHandlingMapEvent.value = false
    }, 2000) // 增加到2000ms
}, 1000)

// 处理订单选择 - 优化版本
const handleOrderSelect = orderId => {
    // 防止循环事件
    if (isHandlingMapEvent.value) {
        return
    }

    // 设置标志防止循环事件
    isHandlingMapEvent.value = true

    try {
        // 查找订单对象前先检查ID的有效性
        if (!orderId) {
            return
        }

        // 使用requestAnimationFrame处理，避免阻塞当前帧渲染
        requestAnimationFrame(() => {
            // 找到订单对象
            const order = orderStore.allOrders.find(o => o.id === orderId)
            if (!order) return

            // 使用orderStore的toggleOrderSelection来修改选择状态
            orderStore.toggleOrderSelection(order)

            // 使用防抖函数发送事件
            debouncedEmitOrderSelected(order)
        })
    } finally {
        // 延迟重置防循环标志
        setTimeout(() => {
            isHandlingMapEvent.value = false
        }, 500) // 增加到500ms
    }
}

// 优化的选择全部订单函数
const handleSelectAll = () => {
    // 防止循环事件
    if (isHandlingMapEvent.value) {
        return
    }

    // 设置标志防止循环事件
    isHandlingMapEvent.value = true

    try {
        // 使用requestAnimationFrame处理，避免阻塞当前帧渲染
        requestAnimationFrame(() => {
            // 过滤可选择的订单
            const selectableOrders = filteredOrders.value.filter(order =>
                order.dispatchStatus === 'pending' || !order.dispatchStatus
            )

            if (selectableOrders.length === 0) {
                return
            }

            // 使用 Set 快速判断选中状态
            const orderIds = selectableOrders.map(o => o.id)
            const selectedIds = new Set(orderStore.selectedOrders.map(o => o.id))

            // 判断是否所有可选订单都已选中
            let allSelected = true
            for (let i = 0; i < orderIds.length; i++) {
                if (!selectedIds.has(orderIds[i])) {
                    allSelected = false
                    break
                }
            }

            // 批处理选择操作，避免大量单独更新
            if (allSelected) {
                // 取消全选
                orderStore.clearSelection()

                // 使用setTimeout延迟发送事件，让UI有时间更新
                setTimeout(() => {
                    // 发送清除选择事件
                    eventBus.emit(EVENT_TYPES.ORDERS_SELECTION_CLEARED)
                }, 50)
            } else {
                // 全选操作 - 使用 orderStore 的 selectOrders 方法
                orderStore.selectOrders(orderIds)

                // 使用setTimeout延迟发送事件，让UI有时间更新
                setTimeout(() => {
                    // 发送批量选择事件
                    eventBus.emit(EVENT_TYPES.ORDERS_BATCH_SELECTED, {
                        selectedOrders: orderStore.selectedOrders
                    })
                }, 50)
            }
        })
    } finally {
        // 延迟重置防循环标志
        setTimeout(() => {
            isHandlingMapEvent.value = false
        }, 500) // 增加到500ms
    }
}

// 清除所有选中状态 - 优化版本
const clearAllSelections = debounce(() => {
    // 防止循环事件
    if (isHandlingMapEvent.value) {
        return
    }

    // 设置标志防止循环事件
    isHandlingMapEvent.value = true

    try {
        // 使用requestAnimationFrame处理，避免阻塞当前帧渲染
        requestAnimationFrame(() => {
            // 清除选择订单
            orderStore.clearSelection()
            // 清除当前选中的单个订单
            orderStore.clearSelectedOrder()
            // 清除路线选择
            if (routeStore.selectedRoute) {
                routeStore.clearSelectedRoute()
            }

            // 使用setTimeout延迟发送事件，让UI有时间更新
            setTimeout(() => {
                // 发送清除选择事件
                eventBus.emit(EVENT_TYPES.ORDERS_SELECTION_CLEARED)
            }, 50)
        })
    } finally {
        // 延迟重置防循环标志
        setTimeout(() => {
            isHandlingMapEvent.value = false
        }, 500) // 增加到500ms
    }
}, 500) // 增加防抖时间到500ms

// 处理分配命令
const handleAssignCommand = (command) => {
    if (command === 'assign') {
        // 普通分配，不自动优化
        batchAssignOrders(false);
    } else if (command === 'assignAndOptimize') {
        // 分配并自动优化
        batchAssignOrders(true);
    }
};

// 批量分配订单 - 优化版本
const batchAssignOrders = async(autoOptimize = false) => {
    if (!selectedDriver.value) return

    // --- 新增：在开头保存司机信息 ---
    const driverToAssign = selectedDriver.value;
    const driverName = driverToAssign?.name || '未知司机'; // 获取司机名称，处理 null 情况
    const driverId = driverToAssign?.id;

    if (!driverId) {
        ElMessage.error('无法获取选定司机的ID');
        return;
    }
    // -------------------------------

    if (isDevMode) {
        performanceMonitor.recordEvent('OrderList:batchAssign:started')
    }

    try {
        const selectedOrders = orderStore.selectedOrders

        // 输出完整的司机信息用于调试
        console.log('完整司机对象:', JSON.stringify(driverToAssign)) // <-- 使用局部变量

        // 从API直接获取最新路线信息
        await routeStore.fetchRoutes({
            date: timeStore.selectedDate,
            shift: timeStore.selectedShift // 传递完整的 shift 对象
        })

        // 获取司机ID
        // const driverId = selectedDriver.value.id // <-- 已在上面获取

        // 从路由存储中查找司机路线 - 使用driver属性和id属性
        const existingRoute = routeStore.routes.find(route =>
            route.driver === driverId // <-- 使用局部变量 driverId
        )

        console.log('所有路线:', routeStore.routes)
        console.log('查找司机路线结果:', existingRoute)

        if (existingRoute && existingRoute.id) {
            console.log('司机已有路线，直接分配订单到路线:', existingRoute.id)

            // 使用路线ID
            const routeId = existingRoute.id

            // 使用API直接分配订单到已有路线
            const id = isDevMode ? performanceMonitor.startMeasure('batchAssign') : -1

            // 调用API分配订单，并接收更新后的本地订单对象数组
            const updatedOrdersFromAssign = await orderStore.assignOrdersToRouteWithAPI( // <--- 接收返回值
                selectedOrders, // <-- 传递完整的订单对象数组
                routeId,
                driverId, // <-- 传递司机ID
                autoOptimize // <-- 传递自动优化参数
            )

            if (isDevMode) {
                const duration = performanceMonitor.endMeasure('batchAssign', id)
                console.log(`批量分配${selectedOrders.length}个订单耗时: ${duration.toFixed(2)}ms`)
            }

            // 检查返回结果是否有效
            if (!updatedOrdersFromAssign || updatedOrdersFromAssign.length === 0) {
                 console.warn('分配订单API调用后未返回有效的订单数据，跳过停靠点更新');
                 orderStore.clearSelectedOrders()
                 orderStore.refreshOrderDisplay()
                 ElMessage.success(`尝试分配 ${selectedOrders.length} 个订单到司机 ${driverName} 的路线完成`);
                 return; // 提前退出
            }

            // 使用返回的更新后订单数据来计算最大停靠点编号
            // 注意：这里假设 updatedOrdersFromAssign 只包含本次分配的订单，
            // 但实际上它可能包含该路线上的所有订单（如果API返回了所有）。
            // 为了安全起见，还是需要从 store 中获取完整的路线订单来计算 maxStopNo。
            const currentRouteOrders = orderStore.getOrdersByRouteNumber(routeId)
            console.log('路线当前订单 (用于计算maxStopNo):', currentRouteOrders)

            let maxStopNo = 0
            currentRouteOrders.forEach(order => {
                if (order.stop_no && order.stop_no > maxStopNo) {
                    maxStopNo = order.stop_no
                }
            })
            console.log('当前路线最大停靠点编号:', maxStopNo)

            // 从 *返回的* 订单数据中筛选出本次操作涉及的订单
            const assignedOrderIds = selectedOrders.map(o => o.id)
            const newlyAssignedOrders = updatedOrdersFromAssign.filter(order => // <--- 使用返回的数据
                assignedOrderIds.includes(order.id)
            )

            // 如果筛选后没有找到对应的订单（理论上不应发生，除非返回数据有问题）
            if (newlyAssignedOrders.length === 0) {
                console.warn('无法从分配结果中找到对应的订单，跳过停靠点更新。返回的数据:', updatedOrdersFromAssign);
                orderStore.clearSelectedOrders();
                orderStore.refreshOrderDisplay();
                ElMessage.success(`尝试分配 ${selectedOrders.length} 个订单到司机 ${driverName} 的路线完成`);
                return; // 提前退出
            }

            console.log('准备更新停靠点的订单对象:', newlyAssignedOrders); // 调试：检查这里的对象是否有 driver_id

            // 逐个更新新分配订单的停靠点编号 (在 newlyAssignedOrders 对象上)
            const updatePromises = []
            newlyAssignedOrders.forEach((order, index) => {
                const newStopNo = maxStopNo + index + 1
                console.log(`为订单 ${order.id} 设置停靠点编号: ${newStopNo}`)

                // 使用API更新停靠点编号
                const updatePromise = orderAPI.updateStopNumber(
                    order.id,
                    newStopNo,
                    order.type || (order.type === 'PICKUP' ? 'PICKUP' : 'DELIVERY')
                ).then(() => {
                    // 同时更新本地订单对象 (这个 order 就是 newlyAssignedOrders 里的对象引用)
                    order.stop_no = newStopNo
                    return order
                })
                updatePromises.push(updatePromise)
            })

            // 等待所有停靠点更新完成
            if (updatePromises.length > 0) {
                await Promise.all(updatePromises)
                console.log('所有停靠点编号更新完成')

                // 检查此时 newlyAssignedOrders 中的数据
                console.log('更新完停靠点后，准备发送事件的订单数据:', newlyAssignedOrders);

                // 通知其他组件订单已更新，使用已更新 stop_no 的 newlyAssignedOrders
                eventBus.emit(EVENT_TYPES.ORDERS_UPDATED, {
                    orders: newlyAssignedOrders, // <--- 使用这些已更新的对象
                    routeName: existingRoute.name,
                    routeId: existingRoute.id,
                    isAssign: true
                })
            }

            orderStore.clearSelectedOrders()
            orderStore.refreshOrderDisplay()
            ElMessage.success(`成功分配 ${selectedOrders.length} 个订单到司机 ${driverName} 的路线`)
        } else {
            console.log('司机没有路线，创建新路线')

            // 创建新路线
            const id = isDevMode ? performanceMonitor.startMeasure('createRoute') : -1

            // 准备订单数据，按类型分类，保持它们在路线中的实际顺序
            const pickups = []
            const deliveries = []

            selectedOrders.forEach((order, index) => {
                const actualStopNo = index + 1  // 在整个路线中的实际位置

                // 处理可能的大小写不一致问题
                if (order.type && (order.type.toUpperCase() === 'PICKUP' || order.type === 'pickup')) {
                    pickups.push({
                        id: order.id,
                        stop_no: actualStopNo  // 使用在路线中的实际位置
                    })
                } else {
                    deliveries.push({
                        id: order.id,
                        stop_no: actualStopNo  // 使用在路线中的实际位置
                    })
                }
            })

            console.log(`构建路线数据: ${pickups.length} 个 pickups, ${deliveries.length} 个 deliveries`)
            console.log(`Pickups stop_no 范围:`, pickups.map(p => p.stop_no))
            console.log(`Deliveries stop_no 范围:`, deliveries.map(d => d.stop_no))

            // 创建路线名称
            const routeName = `${timeStore.selectedDate}-${driverName}-${timeStore.selectedShift?.name || 'shift'}` // 使用 shift name

            // 使用 shiftId 而不是 priority
            const shiftId = timeStore.selectedShift?.id;
            console.log(`使用 shiftId=${shiftId} 创建路线`);

            // 根据班次类型确定起点和终点
            let startPoint = '';
            let endPoint = '';

            // 获取仓库地址ID
            const warehouseAddressId = addressStore.warehouse.id;
            console.log('仓库对象:', addressStore.warehouse);

            // 获取司机家庭地址ID
            const driver = driverStore.getDriverById(driverId);
            const driverAddressId = driver?.address_id || '';

            console.log('仓库地址ID:', warehouseAddressId);
            console.log('司机信息:', {
                id: driver?.id,
                name: driver?.name,
                address_id: driver?.address_id,
                address_lng_lat: driver?.address_lng_lat
            });
            console.log('司机家庭地址ID:', driverAddressId);

            // 根据班次类型设置起点和终点
            if (shiftName.includes('AM')) {
                // 早班: 司机家 -> 仓库
                // 如果司机没有地址，使用仓库地址作为起点
                startPoint = driverAddressId || warehouseAddressId;
                endPoint = warehouseAddressId;
                console.log('AM班次: 起点=司机家, 终点=仓库');
                if (!driverAddressId) {
                    console.warn('司机没有地址信息，使用仓库地址作为起点');
                }
            } else if (shiftName.includes('PM')) {
                // 下午班: 仓库 -> 仓库
                startPoint = warehouseAddressId;
                endPoint = warehouseAddressId;
                console.log('PM班次: 起点=仓库, 终点=仓库');
            } else if (shiftName.includes('NT')) {
                // 晚班: 仓库 -> 司机家
                // 如果司机没有地址，使用仓库地址作为终点
                startPoint = warehouseAddressId;
                endPoint = driverAddressId || warehouseAddressId;
                console.log('NT班次: 起点=仓库, 终点=司机家');
                if (!driverAddressId) {
                    console.warn('司机没有地址信息，使用仓库地址作为终点');
                }
            }

            console.log('最终设置的起点和终点:', {
                startPoint,
                endPoint,
                shiftName
            });

            // 创建路线数据
            const routeData = {
                shift_id: timeStore.selectedShift?.id, // 使用 shift_id 而不是 priority
                date: timeStore.selectedDate,
                name: routeName,
                driver: driverId, // 司机ID
                start_point: startPoint, // 起点地址ID
                end_point: endPoint, // 终点地址ID
                pickups: pickups,
                deliveries: deliveries,
                services: [],
                autoOptimize: autoOptimize // 添加自动优化参数
            }

            console.log('发送创建路线请求:', routeData)

            // 调用API创建路线
            const newRoute = await routeStore.createRouteWithAPI(routeData)

            if (isDevMode) {
                const duration = performanceMonitor.endMeasure('createRoute', id)
                console.log(`创建路线耗时: ${duration.toFixed(2)}ms`)
            }

            console.log('创建路线结果:', newRoute)

            if (!newRoute || !newRoute.id) {
                // 保留之前的错误处理或恢复逻辑
                // 如果之前的修改使得这里总能拿到 newRoute，可以简化为直接抛出错误
                console.warn('创建路线API调用后未返回有效路线对象，尝试恢复');
                // ... (可以保留 handleSuccessfulRouteCreation 的调用作为恢复) ...
                // 或者直接抛出错误：
                throw new Error('创建路线失败且无法恢复');
            }

            // --- 新增：由于 createRoute 已经包含了完整的订单信息和 stop_no，跳过额外的 API 调用 ---
            console.log(`新路线 ${newRoute.id} 创建成功，订单信息已在创建时包含，跳过额外的分配和优化步骤...`);
            try {
                // 由于 createRoute 的 payload 已经包含了完整的 pickups 和 deliveries 信息
                // 包括每个订单的 stop_no，所以不需要再调用 assignOrdersWithOptimization
                // 这样可以避免冗余的 updateStopNumber API 调用

                // 直接更新本地状态，模拟分配完成的效果
                const selectedOrderIds = selectedOrders.map(o => o.id);
                orderStore.updateLocalOrdersWithDriverId(selectedOrderIds, newRoute.id, driverId);

                console.log(`路线 ${newRoute.id} 的订单状态已更新，跳过了 ${selectedOrders.length} 次 updateStopNumber API 调用`);

                // --- 移除以下不再需要的代码段，因为已由assignOrdersWithOptimization完成 ---
                // const refreshedOrders = await orderStore.refreshOrdersFromAPI(
                //     selectedOrders, // 传递完整的订单对象
                //     newRoute.id,
                //     driverId // 传递司机ID
                // );
                // const newlyAssignedOrders = orderStore.getOrdersByRouteNumber(newRoute.id);
                // if (newlyAssignedOrders && newlyAssignedOrders.length > 0) {
                //     let initialStopNo = 0; // 新路线从1开始
                //     const updatePromises = newlyAssignedOrders.map((order, index) => {
                //         const newStopNo = initialStopNo + index + 1;
                //         console.log(`[New Route] 为订单 ${order.id} 设置停靠点编号: ${newStopNo}`);
                //         return orderAPI.updateStopNumber(
                //             order.id,
                //             newStopNo,
                //             order.type || (order.type === 'PICKUP' ? 'PICKUP' : 'DELIVERY')
                //         ).then(() => {
                //             order.stop_no = newStopNo; // 更新本地对象
                //             return order;
                //         });
                //     });
                //     if (updatePromises.length > 0) {
                //         await Promise.all(updatePromises);
                //         console.log('[New Route] 所有停靠点编号更新完成');

                // 发送事件通知其他组件订单状态已更新
                // 重新构建订单数组，保持与 createRoute payload 中相同的 stop_no 逻辑
                const updatedOrders = selectedOrders.map((order, index) => ({
                    ...order,
                    route_id: newRoute.id,
                    driver_id: driverId,
                    stop_no: index + 1  // 使用在路线中的实际位置
                }))

                eventBus.emit(EVENT_TYPES.ORDERS_UPDATED, {
                    orders: updatedOrders,
                    routeName: newRoute.name,
                    routeId: newRoute.id,
                    isAssign: true
                });
            } catch (error) {
                console.error(`分配订单并优化路线 ${newRoute.id} 时出错:`, error);
                ElMessage.error(`分配订单并优化路线时出错: ${error.message}`);
                // 决定是否需要回滚或停止
                return;
            }
            // --------------------------------------

            // 分配路线给司机（如果API创建路线时没有自动分配）
            if (!newRoute.driverId && newRoute.id) {
                await routeStore.assignRouteToDriver(newRoute.id, driverId)
                newRoute.driverId = driverId; // Update local route object too
            }

            orderStore.clearSelectedOrders()

            // 强制刷新所有视图以显示最新状态
            orderStore.refreshOrderDisplay()

            ElMessage.success(`成功为司机 ${driverName} 创建路线并优化分配 ${selectedOrders.length} 个订单`)
        }

        if (isDevMode) {
            performanceMonitor.recordEvent('OrderList:batchAssign:completed')
        }
    } catch (error) {
        console.error('批量分配订单失败:', error)

        // 保存订单和司机信息，以便恢复
        const orderIds = orderStore.selectedOrders.map(o => o.id)
        const driverInfo = selectedDriver.value

        // 从错误响应中提取有用信息的函数
        const extractRouteInfoFromError = (error) => {
            // 情况1: 错误对象本身包含路线信息
            if (error && typeof error === 'object') {
                // 直接包含errCode和id
                if (error.errCode === 365 && error.id) {
                    return { id: error.id, name: error.name, success: true };
                }

                // 检查message字符串
                if (typeof error.message === 'string') {
                    // 尝试解析JSON
                    try {
                        const jsonMatch = error.message.match(/({[\s\S]*})/);
                        if (jsonMatch) {
                            const jsonData = JSON.parse(jsonMatch[1]);
                            if (jsonData.id) {
                                return { id: jsonData.id, name: jsonData.name, success: true };
                            }
                        }
                    } catch (e) {
                        // JSON解析失败，使用正则提取
                    }

                    // 使用正则表达式提取ID
                    const idMatch = error.message.match(/id["']?\s*:\s*["']([^"']+)["']/i);
                    if (idMatch && idMatch[1]) {
                        return { id: idMatch[1], success: true };
                    }
                }
            }

            // 情况2: 错误响应对象包含数据
            if (error.response && error.response.data) {
                const errorData = error.response.data;

                // 检查errCode: 365成功码
                if (errorData.errCode === 365 && errorData.id) {
                    return { id: errorData.id, name: errorData.name, success: true };
                }

                // 检查包含'created'的消息
                if (errorData.id && errorData.msg &&
                    errorData.msg.toLowerCase().includes('created')) {
                    return { id: errorData.id, name: errorData.name, success: true };
                }
            }

            return { success: false };
        };

        // 尝试从错误中提取路线信息
        const routeInfo = extractRouteInfoFromError(error);

        // 如果提取成功
        if (routeInfo.success && routeInfo.id) {
            console.log('从错误中提取到路线信息:', routeInfo);
            handleSuccessfulRouteCreation({ id: routeInfo.id, name: routeInfo.name }, selectedDriver.value);
            return;
        }

        // 如果提取失败，尝试从服务器获取最新路线
        console.log('无法从错误中提取信息，尝试从服务器获取最新路线');

        // 使用 shiftId 而不是 priority
        const shiftId = timeStore.selectedShift?.id;
        console.log(`使用 shiftId=${shiftId} 获取路线`);

        // 立即刷新路线数据
        routeStore.fetchRoutes({
            date: timeStore.selectedDate,
            shift: timeStore.selectedShift // 传递完整的 shift 对象
        }).then(routes => {
            // 查找该司机的最新路线
            const driverId = selectedDriver.value.id;
            const newRoutes = routes.filter(route =>
                (route.driver === driverId || route.driverId === driverId)
            );

            if (newRoutes.length > 0) {
                // 找到路线，假设最后一个是最新的
                const latestRoute = newRoutes[newRoutes.length - 1];
                console.log('找到可能的新路线:', latestRoute);

                // 使用该路线ID
                handleSuccessfulRouteCreation(latestRoute, selectedDriver.value);
                return;
            }

            // 仍然找不到，显示错误消息
            ElMessage.error('批量分配失败，请重试')

            if (isDevMode) {
                performanceMonitor.recordEvent('OrderList:batchAssign:failed')
            }
        }).catch(() => {
            // 刷新路线失败
            ElMessage.error('批量分配失败，请重试')

            if (isDevMode) {
                performanceMonitor.recordEvent('OrderList:batchAssign:failed')
            }
        });
    }
}

// 提取处理成功创建路线的逻辑到单独的函数
const handleSuccessfulRouteCreation = async (routeData, driver) => {
    console.log('处理成功创建路线:', routeData, '司机:', driver)

    if (!routeData || !routeData.id) {
        console.error('路线数据无效，无法处理成功创建')
        return
    }

    const routeId = routeData.id
    const selectedOrderIds = orderStore.selectedOrders.map(o => o.id) // 获取选中的订单ID

    try {
        // --- 新增：在获取路线前，先执行本地乐观更新 ---
        console.log(`[handleSuccessfulRouteCreation] 对路线 ${routeId} 执行本地乐观更新`);
        const driverId = driver ? driver.id : null;
        if (driverId) {
            // 使用直接指定司机ID的方法更新订单状态
            orderStore.updateLocalOrdersWithDriverId(selectedOrderIds, routeId, driverId);
        } else {
            // 使用原来的方法作为备用
            orderStore.updateLocalOrdersAfterAssign(selectedOrderIds, routeId);
        }
        // -----------------------------------------------

        // 刷新路线数据 (可能不需要，因为我们已经有了新路线信息，但保留以防万一)
        await routeStore.fetchRoutes({
            date: timeStore.selectedDate,
            shift: timeStore.selectedShift // 传递完整的 shift 对象
        })

        console.log(`[handleSuccessfulRouteCreation] 处理路线${routeId}的${selectedOrderIds.length}个订单`)

        // 检查选中的订单是否已经分配到路线 (此时应该已经被乐观更新分配了)
        let routeOrders = orderStore.getOrdersByRouteNumber(routeId)
        console.log(`[handleSuccessfulRouteCreation] 路线${routeId}当前有${routeOrders?.length || 0}个订单 (乐观更新后)`)

        // 计算哪些订单尚未分配 (理论上应该为0，除非乐观更新失败或有异步问题)
        const assignedIds = routeOrders ? routeOrders.map(o => o.id) : []
        const unassignedIds = selectedOrderIds.filter(id => !assignedIds.includes(id))

        // 如果有订单尚未分配，尝试手动分配 (这部分逻辑现在更像是备用/恢复机制)
        if (unassignedIds.length > 0) {
            console.warn(`[handleSuccessfulRouteCreation] 发现${unassignedIds.length}个订单在乐观更新后仍未分配，尝试API刷新`)
            try {
                const ordersToRefresh = orderStore.allOrders.filter(o => unassignedIds.includes(o.id));
                const driverId = driver ? driver.id : null;
                await orderStore.refreshOrdersFromAPI(ordersToRefresh, routeId, driverId);
                console.log('[handleSuccessfulRouteCreation] API刷新成功')
                routeOrders = orderStore.getOrdersByRouteNumber(routeId) // 再次获取
            } catch (refreshError) {
                console.error('[handleSuccessfulRouteCreation] API刷新失败:', refreshError)
            }
        }

        // 清除选择
        orderStore.clearSelectedOrders()

        // 发送更新事件 - 使用最新数据
        setTimeout(() => {
            const latestOrders = orderStore.getOrdersByRouteNumber(routeId) // 获取最新的订单
            console.log(`[handleSuccessfulRouteCreation] 发送最终更新事件，路线 ${routeId} 订单数量: ${latestOrders?.length || 0}`);
            if (latestOrders && latestOrders.length > 0) {
                eventBus.emit(EVENT_TYPES.ORDERS_UPDATED, {
                    orders: latestOrders,
                    routeName: routeData.name || `Route-${routeId}`,
                    routeId: routeId,
                    isAssign: true
                })
            } else {
                console.warn(`[handleSuccessfulRouteCreation] 最终发送事件时未找到订单，发送全局更新`);
                eventBus.emit(EVENT_TYPES.ORDERS_UPDATED, {
                    allOrders: true,
                    isAssign: true
                })
            }
        }, 500)

        // 强制刷新所有视图以显示最新状态 (refreshOrderDisplay 可能已经被调用过，但再调用一次确保同步)
        orderStore.refreshOrderDisplay()

        // 检查分配是否成功
        setTimeout(() => {
            const finalOrders = orderStore.getOrdersByRouteNumber(routeId)
            console.log(`[handleSuccessfulRouteCreation] 最终检查：路线${routeId}有${finalOrders?.length || 0}个订单`)
            if (!finalOrders || finalOrders.length === 0) {
                console.warn('[handleSuccessfulRouteCreation] 最终检查未发现订单，数据可能未完全同步')
            }
        }, 1000)

        // 通知用户操作成功
        ElMessage.success(`为司机 ${driver.name} 创建路线成功，路线ID: ${routeId}`)

        if (isDevMode) {
            performanceMonitor.recordEvent('OrderList:batchAssign:completed')
        }
    } catch (error) {
        console.error('[handleSuccessfulRouteCreation] 处理成功创建路线时出错:', error)
        ElMessage.error(`创建路线时发生错误: ${error.message || '未知错误'}`);
        // 即使出错也不影响用户体验，依然显示成功
        // ElMessage.success(`为司机 ${driver.name} 创建路线成功，路线ID: ${routeId}`)

        // if (isDevMode) {
        //     performanceMonitor.recordEvent('OrderList:batchAssign:completed')
        // }
    }
}

// 取消分配订单
const unassignOrders = async() => {
    if (!orderStore.hasAssignedOrders) return

    if (isDevMode) {
        performanceMonitor.recordEvent('OrderList:unassign:started')
    }

    try {
        const selectedOrders = orderStore.selectedOrders.filter(order => order.driver_id || order.route_number)

        // 更详细地记录这些订单的ID和路线/司机信息，包括地图绘制所需的完整信息
        const ordersInfo = selectedOrders.map(order => ({
            id: order.id,
            routeId: order.route_id || order.route_number,
            driverId: order.driver_id,
            stopNo: order.stop_no,
            lng_lat: order.lng_lat,
            address: order.address,
            name: order.name
        }))

        // 确保订单对象保存有原始路线和司机信息
        selectedOrders.forEach(order => {
            // 保存原始路线和司机信息，以便取消分配后能追踪
            order._previousRouteId = order.route_id || order.route_number
            order._previousDriverId = order.driver_id
            order._previousStopNo = order.stop_no
        })

        const id = isDevMode ? performanceMonitor.startMeasure('unassign') : -1

        // 使用API批量取消分配所有选中的已分配订单
        const results = await orderStore.unassignOrdersWithAPI(
            selectedOrders.map(o => o.id)
        )

        if (isDevMode) {
            const duration = performanceMonitor.endMeasure('unassign', id)
            console.log(`批量取消分配${selectedOrders.length}个订单耗时: ${duration.toFixed(2)}ms`)
        }

        // 通知其他组件订单状态已更新
        if (results && Array.isArray(results)) {
            console.log('取消分配结果:', results)

            // 为结果订单添加原始路线和司机信息，以便接收方知道从哪个路线移除了订单
            results.forEach(resultOrder => {
                const originalInfo = ordersInfo.find(info => info.id === resultOrder.id)
                if (originalInfo) {
                    resultOrder._previousRouteId = originalInfo.routeId
                    resultOrder._previousDriverId = originalInfo.driverId
                    resultOrder._previousStopNo = originalInfo.stopNo
                }
            })

            // 按原始路线ID分组，以便发送更精确的更新通知
            const routeGroups = {}
            ordersInfo.forEach(info => {
                if (info.routeId) {
                    if (!routeGroups[info.routeId]) {
                        routeGroups[info.routeId] = []
                    }
                    routeGroups[info.routeId].push(info)
                }
            })

            // 对每个路线发送更新事件
            Object.keys(routeGroups).forEach(routeId => {
                const updatedOrderIds = routeGroups[routeId].map(info => info.id)
                const updatedOrders = results.filter(order => updatedOrderIds.includes(order.id))

                // 确保每个订单都有原始路线信息
                updatedOrders.forEach(order => {
                    if (!order._previousRouteId) {
                        order._previousRouteId = routeId
                    }
                })

                // 发送更新事件通知其他组件
                eventBus.emit(EVENT_TYPES.ORDERS_UPDATED, {
                    orders: updatedOrders,
                    routeId: routeId,
                    isUnassign: true, // 标记这是取消分配操作
                    previousRouteId: routeId // 明确指定原始路线ID
                })
            })

            // 全局更新通知 - 包含所有原始信息
            eventBus.emit(EVENT_TYPES.ORDERS_UPDATED, {
                orders: results,
                isUnassign: true,
                allOrders: true,
                previousRouteIds: Object.keys(routeGroups) // 提供所有受影响的路线ID
            })

            // 延迟通知视图刷新 - 确保数据更新已完成
            setTimeout(() => {
                // 重新获取订单数据
                ordersCache.key = '' // 清除缓存
                createOrdersCache() // 刷新数据
            }, 100)
        }

        orderStore.clearSelectedOrders()

        // 强制刷新所有视图以显示最新状态
        orderStore.refreshOrderDisplay()

        ElMessage.success(`成功取消分配 ${selectedOrders.length} 个订单`)

        if (isDevMode) {
            performanceMonitor.recordEvent('OrderList:unassign:completed')
        }
    } catch (error) {
        console.error('批量取消分配订单失败:', error)
        ElMessage.error('取消分配失败，请重试')
    }
}

// 添加日期时间格式化函数
const formatDateTime = dateTimeStr => {
    if (!dateTimeStr) return ''
    return dayjs(dateTimeStr).format('YYYY-MM-DD HH:mm')
}

// 优化的行类样式计算
const getRowClass = ({ row }) => {
    // 直接使用行的isSelected属性，避免每次查找
    return row.isSelected === true ? 'selected-row' : ''
}

// 为isAllSelected计算属性创建单独的缓存对象
const isAllSelectedCache = ref({
    timestamp: 0,
    result: false,
    orderCount: 0,
    selectedCount: 0
})

// 计算是否全选 - 优化版本
const isAllSelected = computed(() => {
    // 如果缓存时间在100ms内，直接返回缓存结果
    const now = Date.now()
    if (now - isAllSelectedCache.value.timestamp < 100 &&
        isAllSelectedCache.value.orderCount === filteredOrders.value.length &&
        isAllSelectedCache.value.selectedCount === orderStore.selectedOrders.length) {
        return isAllSelectedCache.value.result
    }

    // 只考虑可选择的订单（未分配的订单）
    const selectableOrders = filteredOrders.value.filter(order =>
        order.dispatchStatus === 'pending' || !order.dispatchStatus
    )

    if (selectableOrders.length === 0) {
        isAllSelectedCache.value = {
            timestamp: now,
            result: false,
            orderCount: filteredOrders.value.length,
            selectedCount: orderStore.selectedOrders.length
        }
        return false
    }

    // 计算是否所有可选择的订单都已被选中
    const allSelected = selectableOrders.every(order =>
        orderStore.selectedOrderIds.has(order.id)
    )

    // 更新缓存
    isAllSelectedCache.value = {
        timestamp: now,
        result: allSelected,
        orderCount: filteredOrders.value.length,
        selectedCount: orderStore.selectedOrders.length
    }

    return allSelected
})

// 为isIndeterminate计算属性创建单独的缓存对象
const isIndeterminateCache = ref({
    timestamp: 0,
    result: false,
    orderCount: 0,
    selectedCount: 0
})

// 计算是否部分选中 - 优化版本
const isIndeterminate = computed(() => {
    // 如果缓存时间在100ms内，直接返回缓存结果
    const now = Date.now()
    if (now - isIndeterminateCache.value.timestamp < 100 &&
        isIndeterminateCache.value.orderCount === filteredOrders.value.length &&
        isIndeterminateCache.value.selectedCount === orderStore.selectedOrders.length) {
        return isIndeterminateCache.value.result
    }

    // 只考虑可选择的订单（未分配的订单）
    const selectableOrders = filteredOrders.value.filter(order =>
        order.dispatchStatus === 'pending' || !order.dispatchStatus
    )

    if (selectableOrders.length === 0) {
        isIndeterminateCache.value = {
            timestamp: now,
            result: false,
            orderCount: filteredOrders.value.length,
            selectedCount: orderStore.selectedOrders.length
        }
        return false
    }

    // 使用Set优化查找效率
    const selectedIds = orderStore.selectedOrdersSet ||
        new Set(orderStore.selectedOrders.map(o => o.id))

    // 计算选中数量
    let selectedCount = 0
    for (let i = 0; i < selectableOrders.length; i++) {
        if (selectedIds.has(selectableOrders[i].id)) {
            selectedCount++
        }
    }

    // 部分选中：有选中但不是全部
    const isPartiallySelected = selectedCount > 0 && selectedCount < selectableOrders.length

    // 更新缓存
    isIndeterminateCache.value = {
        timestamp: now,
        result: isPartiallySelected,
        orderCount: filteredOrders.value.length,
        selectedCount: orderStore.selectedOrders.length
    }

    return isPartiallySelected
})

// 添加备份订单相关变量和方法
const isBackingUp = ref(false)

// 备份订单方法
const backupOrders = async() => {
    try {
        isBackingUp.value = true
        const success = await orderStore.orderService.saveOrdersToBackup()
        if (success) {
            ElMessage.success('订单备份成功，文件已下载')
        } else {
            ElMessage.error('订单备份失败')
        }
    } catch (error) {
        console.error('备份订单时出错:', error)
        ElMessage.error('备份订单失败，请重试')
    } finally {
        isBackingUp.value = false
    }
}

// 监控关键操作
watch(showAllOrders, newVal => {
    // 立即更新全局变量，确保地图组件可以访问
    window.showAllOrders = newVal
    console.log('显示模式切换为:', newVal ? '全部订单' : '仅显示未分配订单')

    if (isDevMode) {
        performanceMonitor.recordEvent('OrderList:showAllOrders:changed')
    }
})

// 监听隐藏关闭/异常订单开关变化
watch(hideClosedExceptionalOrders, newVal => {
    console.log('隐藏关闭/异常订单模式切换为:', newVal ? '隐藏' : '显示')

    // 清除缓存，强制重新计算过滤结果
    ordersCache.key = ''

    // 更新全局变量
    window.hideClosedExceptionalOrders = newVal

    if (isDevMode) {
        performanceMonitor.recordEvent('OrderList:hideClosedExceptionalOrders:changed')
    }

    // 防止在处理地图事件时触发
    if (isHandlingMapEvent.value) return

    // 增加锁定时间，避免频繁触发
    isHandlingMapEvent.value = true

    // 清除缓存，强制重新计算
    ordersCache.key = ''

    // 延迟通知更改，给UI渲染留出时间
    setTimeout(() => {
        try {
            // 先发送模式切换事件
            eventBus.emit(EVENT_TYPES.DISPLAY_MODE_CHANGED, {
                showAllOrders: newVal,
                date: timeStore.selectedDate,
                // shift: timeStore.selectedShift
                shiftId: timeStore.selectedShift?.id // 使用 shiftId
            })

            // 使用 requestAnimationFrame 确保在下一帧处理，避免阻塞当前帧
            requestAnimationFrame(() => {
                // 获取更新后的订单列表
                const orders = filteredOrders.value

                // 异步发送事件
                setTimeout(() => {
                    // 使用防抖函数发送订单过滤事件
                    debouncedEmitOrdersFiltered(orders, {
                        showAllOrders: newVal,
                        date: timeStore.selectedDate,
                        // shift: timeStore.selectedShift
                        shiftId: timeStore.selectedShift?.id // 使用 shiftId
                    })
                }, 50)
            })
        } catch (error) {
            console.error('切换显示模式失败:', error)
        } finally {
            // 延长锁定时间到1000ms
            setTimeout(() => {
                isHandlingMapEvent.value = false
            }, 1000)
        }
    }, 20)
}, { immediate: false })

// 表格容器引用
const tableContainer = ref(null)

// 使用虚拟滚动优化大数据量渲染
const { visibleItems, totalHeight, offsetY } = useVirtualScroll({
    items: computed(() => filteredOrders.value),
    itemHeight: 28, // 行高
    buffer: 10, // 缓冲项数
    container: tableContainer
})

// 跟踪是否已应用虚拟滚动
const isVirtualScrollEnabled = ref(false)

// 根据数据量动态启用虚拟滚动
const dynamicTableData = computed(() => {
    const orders = filteredOrders.value

    // 只有当订单数量超过阈值时才使用虚拟滚动
    const threshold = 200 // 阈值：200条数据
    isVirtualScrollEnabled.value = orders.length > threshold

    return isVirtualScrollEnabled.value ? visibleItems.value : orders
})

// 虚拟滚动样式
const virtualScrollStyle = computed(() => {
    if (!isVirtualScrollEnabled.value) {
        return {}
    }

    return {
        height: `${totalHeight.value}px`,
        position: 'relative'
    }
})

// 虚拟滚动内容样式
const virtualScrollContentStyle = computed(() => {
    if (!isVirtualScrollEnabled.value) {
        return {}
    }

    return {
        transform: `translateY(${offsetY.value}px)`,
        position: 'absolute',
        width: '100%'
    }
})

// 表格引用
const tableRef = ref(null)

// 添加状态相关处理函数
const getStatusText = status => {
    if (!status) return '未知'

    const statusMap = {
        'processing': '处理中',
        'sorting': '分拣中',
        'pickedUp': '已取件',
        'transporting': '运输中',
        'delivered': '已送达'
    }

    return statusMap[status] || status
}

const getStatusTagType = status => {
    if (!status) return 'info'

    const typeMap = {
        'processing': 'info',
        'sorting': 'warning',
        'pickedUp': 'warning',
        'transporting': 'primary',
        'delivered': 'success'
    }

    return typeMap[status] || 'info'
}

// 根据路线ID获取司机名字
const getDriverNameByRouteId = (routeId) => {
    // 寻找对应的路线
    const route = routeStore.routes.find(route => route.id === routeId);

    // 如果找到路线并且路线有司机ID
    if (route && route.driver) {
        // 根据司机ID获取司机对象
        const driver = driverStore.getDriverById(route.driver);

        // 如果找到司机，返回司机名字
        if (driver) {
            return driver.name;
        }
    }

    // 如果没有找到路线或司机，返回null
    return null;
}

// 根据司机ID获取司机名字，添加空值检查
const getDriverNameById = (driverId) => {
    if (!driverId) return '未知司机';

    const driver = driverStore.getDriverById(driverId);
    return driver ? driver.name : '未知司机';
}
</script>

<style scoped>
.order-list-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: #fff;
    border-radius: 4px;
    overflow: hidden;
    max-height: calc(100vh - var(--g-header-height));
}

.header {
    padding: 10px;
    border-bottom: 1px solid #eee;
}

.all-controls-row {
    padding: 5px 10px;
    display: flex;
    align-items: center;
    gap: 5px;
    flex-wrap: nowrap;
    background-color: #f9f9f9;
    border-bottom: 1px solid #eee;
    white-space: nowrap;
    overflow-x: auto;
}

.all-controls-row :deep(.el-input__wrapper),
.all-controls-row :deep(.el-date-editor),
.all-controls-row :deep(.el-select),
.all-controls-row :deep(.el-switch) {
    max-width: none;
    width: auto;
}

/* 增加班次选择下拉菜单的宽度 */
.shift-select {
    min-width: 150px;
    width: 150px;
}

.orders-wrapper {
    flex: 1;
    overflow: auto;
    padding: 0;
    position: relative; /* 为虚拟滚动添加 */
}

/* 虚拟滚动样式 */

.virtual-table-wrapper {
    width: 100%;
    position: relative;
}

.virtual-table-content {
    width: 100%;
}

:deep(.el-table) {
    font-size: 12px;
}

:deep(.el-table__header-wrapper th) {
    height: 32px;
    padding: 4px 0;
    background-color: #f5f7fa;
}

:deep(.el-table__row) {
    height: 28px;
}

:deep(.el-table__cell) {
    padding: 2px 4px;
}

:deep(.el-table__expand-icon) {
    font-size: 12px;
    transform: rotate(0deg);
}

:deep(.el-table__expand-icon--expanded) {
    transform: rotate(90deg);
}

.address-cell,
.remark-cell,
.order-no,
.driver-name,
.route-name {
    display: inline-block;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.order-detail {
    padding: 8px 12px;
    background-color: #f9f9f9;
    font-size: 12px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 8px;
}

.order-detail-item {
    line-height: 1.5;
}

.order-detail-item strong {
    font-weight: 600;
    color: #606266;
}

.selected-row {
    background-color: #ecf5ff !important;
}

.search-input {
    min-width: 140px;
    max-width: 100px;
    width: 100px;
}

.selected-count {
    margin: 0 4px;
    font-size: 12px;
    color: #409eff;
}

.stop-number {
    margin-left: 4px;
    color: #909399;
    font-size: 11px;
}

@media (width <= 1200px) {

    .all-controls-row :deep(.el-input__wrapper),
    .all-controls-row :deep(.el-date-editor) {
        max-width: 100px;
    }

    .all-controls-row :deep(.el-select) {
        max-width: 130px;
    }

    /* 即使在窄屏设备上也保持班次选择的宽度 */
    .shift-select {
        min-width: 130px;
        width: 130px;
    }
}

/* 添加高亮和悬停效果 */

:deep(.el-table__row:hover) {
    background-color: #f0f9ff !important;
}

:deep(.order-table .el-table__row-level-0) {
    cursor: pointer;
}

/* 优化表格内容垂直对齐 */

:deep(.el-table .cell) {
    display: flex;
    align-items: center;
}

/* 移动设备响应式优化 */

@media (width <= 768px) {

    :deep(.el-table__cell) {
        padding: 1px 2px;
    }

    .order-detail {
        grid-template-columns: 1fr;
    }
}

/* 全局样式，确保表格元素正确对齐 */

.el-table .el-tag {
    margin: 0 auto;
}

.el-table__expand-icon {
    margin-right: 0 !important;
}

.order-table .el-checkbox {
    margin-right: 0;
}

.el-table--small .el-table__cell {
    padding: 2px 0;
}

.el-table--border .el-table__cell:first-child {
    padding-left: 4px;
}

.el-table--border .el-table__cell:last-child {
    padding-right: 4px;
}

.el-checkbox__input {
    line-height: 1;
}

.order-count {
    font-size: 14px;
    color: #606266;
    margin-left: 4px;
}
</style>