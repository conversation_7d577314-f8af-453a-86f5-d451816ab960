import { RouteRepository } from '../repositories/RouteRepository'
import { OrderRepository } from '../repositories/OrderRepository'
import { Route } from '../models/Route'
import { useAddressStore } from '../stores/address'
import { EVENT_TYPES } from '../utils/eventBus'

// HERE API Key
const HERE_API_KEY = 'e6h7jZODoCtiZWKNeei-vVeoSt4C436p8HvaOD8nSuU'

export class RouteService {
    constructor() {
        this.routeRepository = new RouteRepository()
        this.orderRepository = new OrderRepository()

        // 本地状态
        this.routes = []
        this.currentRouteNumber = 0

        // 事件总线
        this.eventBus = null
        if (typeof window !== 'undefined') {
            this.eventBus = window.eventBus
        }
    }

    // 获取路线数据
    async fetchRoutes(params) {
        try {
            // 检查和规范化参数
            if (!params) {
                console.error('RouteService.fetchRoutes: 参数为空')
                return []
            }

            // 确保传递的参数格式正确
            const fetchParams = {
                date: params.date
            }

            // 详细记录传入的参数
            console.log('RouteService.fetchRoutes 接收到的参数:', JSON.stringify(params, null, 2));

            // 检查 shift 参数
            if (params.shift) {
                console.log('shift 参数类型:', typeof params.shift);
                console.log('shift 参数内容:', JSON.stringify(params.shift, null, 2));

                // 如果直接提供了 priority，使用它
                if (params.priority) {
                    fetchParams.priority = typeof params.priority === 'object' ?
                        params.priority.value : params.priority;
                    console.log(`使用直接提供的 priority=${fetchParams.priority}`);
                }
                // 否则，根据 shift 的 name 设置 priority
                else {
                    const shiftObj = typeof params.shift === 'object' ? params.shift : null;

                    // 记录 shift 对象的所有属性
                    if (shiftObj) {
                        console.log('shift 对象的所有属性:');
                        for (const key in shiftObj) {
                            console.log(`  ${key}: ${JSON.stringify(shiftObj[key])}`);
                        }
                    }

                    // 尝试获取 name 属性
                    const shiftName = shiftObj?.name || '';
                    console.log(`获取到的 shiftName: "${shiftName}"`);

                    // 根据班次名称设置 priority: AM=2, PM=4, NT=6
                    if (shiftName && shiftName.includes('AM')) {
                        fetchParams.priority = 2;
                        console.log(`根据班次名称 "${shiftName}" 设置 priority=2 (AM)`);
                    } else if (shiftName && shiftName.includes('PM')) {
                        fetchParams.priority = 4;
                        console.log(`根据班次名称 "${shiftName}" 设置 priority=4 (PM)`);
                    } else if (shiftName && shiftName.includes('NT')) {
                        fetchParams.priority = 6;
                        console.log(`根据班次名称 "${shiftName}" 设置 priority=6 (NT)`);
                    } else {
                        // 尝试从 label 属性获取班次信息
                        const shiftLabel = shiftObj?.label || '';
                        console.log(`尝试从 label 获取班次信息: "${shiftLabel}"`);

                        if (shiftLabel.includes('AM')) {
                            fetchParams.priority = 2;
                            console.log(`根据班次标签 "${shiftLabel}" 设置 priority=2 (AM)`);
                        } else if (shiftLabel.includes('PM')) {
                            fetchParams.priority = 4;
                            console.log(`根据班次标签 "${shiftLabel}" 设置 priority=4 (PM)`);
                        } else if (shiftLabel.includes('NT')) {
                            fetchParams.priority = 6;
                            console.log(`根据班次标签 "${shiftLabel}" 设置 priority=6 (NT)`);
                        } else {
                            // 如果无法确定班次，使用 shift 的 value 作为 priority
                            fetchParams.priority = shiftObj?.value || params.shift;
                            console.log(`无法从名称或标签确定班次，使用 value 作为 priority=${fetchParams.priority}`);
                        }
                    }
                }
            }
            // 兼容旧代码 - 如果直接提供了 priority 参数
            else if (params.priority) {
                fetchParams.priority = typeof params.priority === 'object' ?
                    params.priority.value : params.priority;
                console.log(`使用直接提供的 priority=${fetchParams.priority}`);
            } else {
                console.log('未提供 shift 或 priority 参数');
            }

            console.log('RouteService.fetchRoutes 使用参数:', fetchParams)

            const routes = await this.routeRepository.getRoutes(fetchParams)
            this.routes = routes

            // 更新当前路线编号，确保新建的路线不会与已有的冲突
            this.updateCurrentRouteNumber()

            return routes
        } catch (error) {
            console.error('获取路线失败:', error)
            throw error
        }
    }

    // 更新当前路线编号
    updateCurrentRouteNumber() {
        if (this.routes.length > 0) {
            const maxRouteNumber = Math.max(
                ...this.routes.map(route => Number(route.routeNumber) || 0)
            )
            this.currentRouteNumber = maxRouteNumber + 1
        }
    }

    // 获取当前所有路线的 bucket_sn 集合
    getCurrentBuckets() {
        console.log('[RouteService] 获取当前所有路线的 bucket_sn...');
        const bucketSet = new Set();

        this.routes.forEach(route => {
            if (route.bucket_sn) {
                // API 返回的是字符串，转换为数字后添加到集合
                const bucketNum = parseInt(route.bucket_sn, 10);
                if (!isNaN(bucketNum)) {
                    bucketSet.add(bucketNum);
                }
            }
        });

        console.log('[RouteService] 当前路线的 bucket_sn 集合:', Array.from(bucketSet).sort((a, b) => a - b));
        return bucketSet;
    }

    // 获取下一个可用的 bucket_sn
    getNextAvailableBucket() {
        console.log('[RouteService] 计算下一个可用的 bucket_sn...');
        const currentBuckets = this.getCurrentBuckets();

        // 如果没有任何 bucket，返回 1
        if (currentBuckets.size === 0) {
            console.log('[RouteService] 没有现有 bucket，返回 1');
            return 1;
        }

        // 找到最小的未使用的数字
        let nextBucket = 1;
        while (currentBuckets.has(nextBucket)) {
            nextBucket++;
            // 安全检查，避免无限循环（最多支持100个路线）
            if (nextBucket > 100) {
                console.error('[RouteService] bucket_sn 已达到最大值 100');
                throw new Error('已达到最大路线数量限制 (100)');
            }
        }

        console.log('[RouteService] 计算出的下一个可用 bucket_sn:', nextBucket);
        return nextBucket;
    }

    // 创建新路线
    createRoute(driverId, orders = []) {
    // 检查司机是否已有活动路线
        const existingRoute = this.getDriverActiveRoute(driverId)
        if (existingRoute) {
            return existingRoute
        }

        // 获取司机信息，以便获取颜色
        let driverColor = null
        try {
            // 尝试从全局 driverStore 获取司机信息
            if (window.driverStore) {
                const driver = window.driverStore.getDriverById(driverId)
                if (driver && driver.color) {
                    driverColor = driver.color
                    console.log(`从 driverStore 获取到司机 ${driverId} 的颜色: ${driverColor}`)
                }
            }
        } catch (error) {
            console.warn('获取司机颜色时出错:', error)
        }

        // 创建新路线
        const routeNumber = this.currentRouteNumber++
        const routeData = {
            routeNumber,
            driverId,
            status: 'active',
            // 使用司机的颜色
            color: driverColor,
            orders: orders.map((order, index) => ({
                orderId: order.id,
                stopNumber: index + 1
            }))
        }

        // 创建Route对象
        const route = new Route(routeData)

        // 添加到路线列表
        this.routes.push(route)
        return route
    }

    // 使用API创建路线
    async createRouteWithAPI(routeData) {
        try {
            // 自动分配 bucket_sn
            if (!routeData.bucket_sn) {
                const nextBucket = this.getNextAvailableBucket();
                routeData.bucket_sn = nextBucket.toString(); // API 期望字符串格式
                console.log(`[RouteService] 为新路线自动分配 bucket_sn: ${routeData.bucket_sn}`);
            }

            // 获取司机信息，以便获取颜色
            let driverColor = null
            if (routeData.driverId) {
                try {
                    // 尝试从全局 driverStore 获取司机信息
                    if (window.driverStore) {
                        const driver = window.driverStore.getDriverById(routeData.driverId)
                        if (driver && driver.color) {
                            driverColor = driver.color
                            console.log(`从 driverStore 获取到司机 ${routeData.driverId} 的颜色: ${driverColor}`)

                            // 添加颜色到路线数据
                            routeData.color = driverColor
                        }
                    }
                } catch (error) {
                    console.warn('获取司机颜色时出错:', error)
                }
            }

            const route = await this.routeRepository.createRouteWithAPI(routeData)

            if (route) {
                // 如果路线没有颜色，但我们获取到了司机颜色，设置路线颜色
                if (!route.color && driverColor) {
                    route.color = driverColor
                }

                // 添加到路线列表
                this.routes.push(route)

                // 更新当前路线编号
                this.updateCurrentRouteNumber()

                // 自动优化路线
                if (routeData.autoOptimize !== false) { // 默认启用自动优化，除非明确禁用
                    console.log(`路线 ${route.id} 创建成功，正在自动优化路线顺序...`);

                    // 延迟一小段时间，确保路线和订单数据已同步
                    setTimeout(async () => {
                        try {
                            // 获取订单数据
                            const orderStore = window.orderStore;
                            if (orderStore) {
                                // 确保订单数据已刷新
                                orderStore.invalidateCache();

                                // 获取路线订单
                                const routeOrders = orderStore.getOrdersByRouteNumber(route.id);

                                if (routeOrders && routeOrders.length > 1) {
                                    console.log(`路线 ${route.id} 有 ${routeOrders.length} 个订单，开始自动优化`);
                                    await this.optimizeRouteWithHERE(route);
                                } else {
                                    console.log(`路线 ${route.id} 订单数量不足 (${routeOrders?.length || 0})，跳过自动优化`);
                                }
                            }
                        } catch (optimizeError) {
                            console.error(`自动优化路线 ${route.id} 失败:`, optimizeError);
                        }
                    }, 1000); // 延迟1秒，确保数据已同步
                }
            }

            return route
        } catch (error) {
            console.error('创建路线失败:', error)
            throw error
        }
    }

    // 使用HERE API优化路线顺序
    async optimizeRouteWithHERE(route) {
        if (!route) {
            console.error('优化路线失败: 路线对象为空');
            return null;
        }

        console.log(`开始优化路线 ${route.id} 的顺序`);

        // 标记路线正在优化
        route.isHereOptimizing = true;

        try {
            // 检查API Key
            if (!HERE_API_KEY) {
                console.warn("未配置 HERE API Key，无法进行优化");
                route.isHereOptimizing = false;
                return null;
            }

            // 获取订单数据
            const orderStore = window.orderStore;
            if (!orderStore) {
                console.error('优化路线失败: 无法访问订单存储');
                route.isHereOptimizing = false;
                return null;
            }

            // 获取路线订单
            const routeOrders = orderStore.getOrdersByRouteNumber(route.id);
            if (!routeOrders || routeOrders.length === 0) {
                console.warn(`路线 ${route.id} 没有订单，无法优化`);
                route.isHereOptimizing = false;
                return null;
            }

            console.log(`[HERE Optimize] 路线 ${route.id} 包含 ${routeOrders.length} 个订单`);

            // 筛选有坐标的订单
            const ordersWithCoords = routeOrders.filter(order =>
                (order.lng_lat && Array.isArray(order.lng_lat) && order.lng_lat.length === 2) ||
                (order.location && order.location.latitude && order.location.longitude)
            );

            if (ordersWithCoords.length === 0) {
                console.warn('订单没有有效坐标信息，无法优化路线');
                route.isHereOptimizing = false;
                return null;
            }

            console.log(`[HERE Optimize] 有效坐标订单数量: ${ordersWithCoords.length}`);

            // 获取路线的起点和终点
            let startPoint = "43.818770,-79.345225"; // 默认仓库坐标，格式: lat,lon
            let endPoint = "43.818770,-79.345225";   // 默认仓库坐标，格式: lat,lon

            // 获取完整的路线对象，以便获取起点和终点信息
            const fullRoute = await this.routeRepository.getRouteById(route.id);
            console.log(`[HERE Optimize] 获取到完整路线信息:`, fullRoute);

            // 根据路线的起点和终点获取坐标
            if (fullRoute) {
                // 导入地址存储
                const addressStore = useAddressStore();

                // 确保地址存储已初始化
                addressStore.initialize();

                // 获取司机ID，以便检查地址
                const driverId = fullRoute.driver || fullRoute.driverId;

                // 获取起点坐标
                if (fullRoute.start_point) {
                    const startCoords = addressStore.getCoordinatesByAddressId(fullRoute.start_point);
                    if (startCoords && Array.isArray(startCoords) && startCoords.length === 2) {
                        startPoint = `${startCoords[0]},${startCoords[1]}`;
                        console.log(`[HERE Optimize] 使用路线起点坐标: ${startPoint}`);
                    }
                }

                // 获取终点坐标
                if (fullRoute.end_point) {
                    const endCoords = addressStore.getCoordinatesByAddressId(fullRoute.end_point);
                    if (endCoords && Array.isArray(endCoords) && endCoords.length === 2) {
                        endPoint = `${endCoords[0]},${endCoords[1]}`;
                        console.log(`[HERE Optimize] 使用路线终点坐标: ${endPoint}`);
                    }
                }
            }

            // 创建 waypoint 映射，用于跟踪订单ID和waypoint ID的对应关系
            const orderIdToWaypointId = new Map();

            // 构建 waypoints 数组
            let waypoints = [];

            // 添加起点
            waypoints.push(`start;${startPoint}`);

            // 添加订单点
            ordersWithCoords.forEach((order, index) => {
                // 获取订单坐标
                const coords = order.lng_lat || order.location;
                if (!coords || !Array.isArray(coords) || coords.length !== 2) return;

                // 创建唯一的 waypoint ID
                const waypointId = `order_${order.id}`;

                // 添加到映射
                orderIdToWaypointId.set(waypointId, order);

                // 添加到 waypoints 数组，格式: id;lat,lon
                waypoints.push(`${waypointId};${coords[0]},${coords[1]}`);
            });

            // 添加终点
            waypoints.push(`end;${endPoint}`);

            // 构建 HERE API URL
            let apiUrl = `https://wse.ls.hereapi.com/2/findsequence.json?`;
            apiUrl += `apiKey=${HERE_API_KEY}`;
            apiUrl += `&start=start`;
            apiUrl += `&end=end`;
            apiUrl += `&improveFor=time`; // 优化时间
            apiUrl += `&mode=fastest;car;traffic:disabled`; // 使用最快路线，不考虑交通

            // 添加所有 waypoints
            waypoints.forEach(waypoint => {
                apiUrl += `&waypoint=${encodeURIComponent(waypoint)}`;
            });

            apiUrl += `&mode=fastest;car;tollroad:-2`; // 使用 tollroad:-2 强烈避免收费公路
            apiUrl += '&clustering=drivingDistance';
            console.log(`[HERE Optimize] 请求 URL (已隐藏 API Key): ${apiUrl.replace(HERE_API_KEY, 'HIDDEN_API_KEY')}`);

            // 发送请求
            const response = await fetch(apiUrl);
            console.log(`[HERE Optimize] 收到响应，状态码: ${response.status}`);

            if (!response.ok) {
                const errorText = await response.text();
                console.error(`[HERE Optimize] API 请求失败: ${response.status} ${response.statusText}`, errorText);
                route.isHereOptimizing = false;
                return null;
            }

            // 解析响应
            const data = await response.json();
            console.log('[HERE Optimize] 收到响应数据:', data);

            if (!data || !data.results || data.results.length === 0) {
                console.error('[HERE Optimize] 响应数据无效');
                route.isHereOptimizing = false;
                return null;
            }

            // 提取优化后的路径点顺序
            const optimizedWaypoints = data.results[0].waypoints;
            console.log('[HERE Optimize] 优化后的路径点:', optimizedWaypoints);

            const sortedOrders = [];
            // 遍历优化后的路径点 (排除起点和终点)
            optimizedWaypoints.forEach(waypoint => {
                // 查找原始订单
                const originalOrder = orderIdToWaypointId.get(waypoint.id);
                if (originalOrder) {
                    sortedOrders.push(originalOrder);
                    console.log(`[HERE Optimize] 添加优化订单: ID=${originalOrder.id}, 原 Waypoint ID=${waypoint.id}`);
                } else if (waypoint.id !== 'start' && waypoint.id !== 'end') {
                    console.warn(`[HERE Optimize] 未能通过 Waypoint ID "${waypoint.id}" 找到对应的原始订单`);
                }
            });

            console.log(`[HERE Optimize] 按优化顺序排序后的订单: ${sortedOrders.length} 个`);
            console.log('优化后顺序:', sortedOrders.map((o, idx) => `[${idx+1}] ${o.id} (${o.name || '未知'})`));

            // 确保所有原始订单都包含在结果中 (包括无坐标的)
            const includedOrderIds = new Set(sortedOrders.map(order => order.id));
            const missingOrders = routeOrders.filter(order => !includedOrderIds.has(order.id));
            if (missingOrders.length > 0) {
                console.log(`[HERE Optimize] 添加 ${missingOrders.length} 个因无坐标或其他原因未参与优化的订单到末尾:`, missingOrders.map(o => o.id));
                sortedOrders.push(...missingOrders);
            }

            // 更新UI和状态
            const updatedOrders = sortedOrders.map((order, index) => ({
                ...order,
                stop_no: index + 1
            }));

            // 使用OrderStore的updateLocalOrderSequence方法更新订单顺序
            if (orderStore.updateLocalOrderSequence) {
                orderStore.updateLocalOrderSequence(route.id, updatedOrders);
            }

            // 发送地图刷新事件
            if (this.eventBus) {
                // 发送 MAP_REFRESH 事件，通知地图更新标记和路线
                this.eventBus.emit(EVENT_TYPES.MAP_REFRESH, {
                    routeId: route.id,
                    orders: updatedOrders,
                    needRedrawRoute: true, // 添加标志，指示需要重新绘制路线
                    timestamp: Date.now()
                });

                // 发送 ORDERS_UPDATED 事件，通知 DriverRoutePanel 更新
                this.eventBus.emit(EVENT_TYPES.ORDERS_UPDATED, {
                    orders: updatedOrders,
                    routeId: route.id,
                    routeName: route.name || `路线 ${route.id}`,
                    isHereOptimize: true, // 标记这是 HERE 优化操作
                    timestamp: Date.now()
                });

                console.log(`[HERE Optimize] 路线 ${route.name || route.id} 订单顺序优化完成，已请求重新绘制路线和更新面板`);
            }

            return updatedOrders;
        } catch (error) {
            console.error('[HERE Optimize] 优化路线失败:', error);
            return null;
        } finally {
            route.isHereOptimizing = false; // 结束加载状态
        }
    }

    // 分配路线给司机
    async assignRouteToDriver(routeId, driverId) {
        try {
            const success = await this.routeRepository.assignRouteToDriver(routeId, driverId)

            if (success) {
                // 获取司机信息，以便获取颜色
                let driverColor = null
                try {
                    // 尝试从全局 driverStore 获取司机信息
                    if (window.driverStore) {
                        const driver = window.driverStore.getDriverById(driverId)
                        if (driver && driver.color) {
                            driverColor = driver.color
                            console.log(`从 driverStore 获取到司机 ${driverId} 的颜色: ${driverColor}`)
                        }
                    }
                } catch (error) {
                    console.warn('获取司机颜色时出错:', error)
                }

                // 更新本地状态
                const route = this.routes.find(r => r.routeNumber === routeId)
                if (route) {
                    route.driverId = driverId

                    // 更新路线颜色为司机颜色
                    if (driverColor) {
                        route.color = driverColor
                        console.log(`已将路线 ${routeId} 的颜色更新为司机 ${driverId} 的颜色: ${driverColor}`)
                    }
                }
            }

            return success
        } catch (error) {
            console.error(`将路线 ${routeId} 分配给司机 ${driverId} 失败:`, error)
            throw error
        }
    }

    // 更新路线状态
    async updateRouteStatus(routeId, status) {
        try {
            await this.routeRepository.updateRouteStatus(routeId, status)

            // 更新本地状态
            const route = this.routes.find(r => r.routeNumber === routeId)
            if (route) {
                route.updateStatus(status)
            }

            return route
        } catch (error) {
            console.error('更新路线状态失败:', error)
            throw error
        }
    }

    // 更新路线订单
    updateRouteOrders(routeNumber, orders) {
        const route = this.routes.find(r => r.routeNumber === routeNumber)
        if (!route) return null

        // 转换订单为Route需要的格式
        const routeOrders = orders.map((order, index) => ({
            orderId: order.id,
            stopNumber: index + 1
        }))

        // 更新路线的订单
        route.orders = routeOrders

        return route
    }

    // 获取司机的活动路线
    getDriverActiveRoute(driverId) {
        return this.routes.find(r => r.driverId === driverId && r.status === 'active')
    }

    // 获取所有活动路线
    getActiveRoutes() {
        return this.routes.filter(r => r.status === 'active')
    }

    // 添加订单到路线
    async addOrderToRoute(order, routeNumber) {
        const route = this.routes.find(r => r.routeNumber === routeNumber)
        if (!route) return null

        // 将订单添加到路线
        route.addOrder(order)

        // 更新订单的路线信息
        order.assignRoute(routeNumber, route.orders.length)

        return route
    }

    // 从路线中移除订单
    async removeOrderFromRoute(orderId, routeNumber) {
        const route = this.routes.find(r => r.routeNumber === routeNumber)
        if (!route) return null

        // 从路线中移除订单
        route.removeOrder(orderId)

        return route
    }
}