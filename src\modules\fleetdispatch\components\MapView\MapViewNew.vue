<template>
    <div class="map-container">
        <div id="map" ref="mapContainer" style="width: 100%; height: 100%;" />

        <div class="route-type-control">
            <label>
                <input v-model="routeType" type="radio" value="direct" @change="redrawRoutes">
                直线路线
            </label>
            <label>
                <input v-model="routeType" type="radio" value="realistic" @change="redrawRoutes">
                真实道路
            </label>
        </div>

        <!-- 添加交通图层控制 -->
        <div class="traffic-control">
            <label>
                <input v-model="showTraffic" type="checkbox" @change="toggleTraffic">
                实时交通
            </label>
        </div>

        <!-- 添加自动缩放控制 -->
        <div class="zoom-control">
            <label>
                <input v-model="autoZoom" type="checkbox">
                自动缩放
            </label>
        </div>

        <!-- 添加矩形选择框 -->
        <div v-show="isDrawingSelectionBox" ref="selectionBox" class="selection-box" />

        <!-- 添加选择提示 -->
        <div v-show="showSelectionTooltip" class="selection-tooltip">
            {{ selectionTooltipContent }}
        </div>

        <div v-if="performanceStats" class="performance-stats">
            <div>FPS: {{ performanceStats.fps.toFixed(1) }}</div>
            <div>Markers: {{ performanceStats.markerCount }}</div>
            <div>Render Time: {{ performanceStats.renderTime.toFixed(1) }}ms</div>
            <div>Processing Time: {{ performanceStats.processingTime?.toFixed(1) || 0 }}ms</div>
            <div>Total Points: {{ performanceStats.totalPoints }}</div>
            <div>Visible Points: {{ performanceStats.visibleCount }}</div>
        </div>

        <!-- 添加司机位置显示控制 -->
        <div class="driver-control">
            <label>
                <input v-model="showDrivers" type="checkbox" @change="toggleDrivers">
                显示司机位置
            </label>
        </div>

        <!-- 添加司机家庭地址显示控制 -->
        <div class="driver-home-control">
            <label>
                <input v-model="showDriverHomes" type="checkbox" @change="toggleDriverHomes">
                显示司机家庭地址
            </label>
        </div>

        <!-- 添加仓库地址显示控制 -->
        <div class="warehouse-control">
            <label>
                <input v-model="showWarehouse" type="checkbox" @change="toggleWarehouse">
                显示仓库地址
            </label>
        </div>



        <!-- 添加保存路线按钮 -->
        <div v-if="showSaveRouteButton" class="save-route-button">
            <el-button
                type="primary"
                size="small"
                :loading="isSavingRoute"
                @click="saveRouteOrder"
            >
                <el-icon><Promotion /></el-icon>
                保存路线顺序
            </el-button>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted, onBeforeUnmount, onUnmounted, nextTick, provide } from 'vue'
import { useMapStore } from '../../stores/map'
import { useOrderStore } from '../../stores/order'
import { useDriverStore } from '../../stores/driver'
import { useRouteStore } from '../../stores/route'
import { useTimeStore } from '../../stores/time'
import { useAddressStore } from '../../stores/address'
import { eventBus, EVENT_TYPES } from '../../utils/eventBus'
import { debounce } from 'lodash'
import maplibregl from 'maplibre-gl'
import 'maplibre-gl/dist/maplibre-gl.css'
import { ElMessage } from 'element-plus'
import { Promotion } from '@element-plus/icons-vue'

// 导入各个拆分的composables
import { useMapInitialization } from './composables/useMapInitialization'
import { useMarkerManagement } from './composables/useMarkerManagement'
import { useDriverMarkers } from './composables/useDriverMarkers'
import { useDriverHomeMarkers } from './composables/useDriverHomeMarkers'
import { useWarehouseMarker } from './composables/useWarehouseMarker'
import { useRouteManagement } from './composables/useRouteManagement'
import { useMapInteractions } from './composables/useMapInteractions'
import { useTrafficLayer } from './composables/useTrafficLayer'
import { usePerformanceMonitor } from './composables/usePerformanceMonitor'
import { useGeocoding } from './composables/useGeocoding'
import { useMapSymbolDragging } from './composables/useMapSymbolDragging'

// 初始化各个store
const mapStore = useMapStore()
const orderStore = useOrderStore()
const driverStore = useDriverStore()
const routeStore = useRouteStore()
const timeStore = useTimeStore()
const addressStore = useAddressStore()

// 初始化地址存储
addressStore.initialize()

// 基础地图设置
const mapContainer = ref(null)
const isVisible = ref(true)
const autoZoom = ref(false) // 默认关闭自动缩放

// 添加订单过滤键
const ordersFilterKey = ref(Date.now().toString())

// 使用各个composables
const { map, isMapLoaded, initializeMap, removeMap, moveSymbolLayersToTop } = useMapInitialization(mapContainer)

// 使用性能监控
const { performanceStats, updatePerformanceStats } = usePerformanceMonitor()

// 使用交通图层管理
const { showTraffic, addTrafficLayer, toggleTraffic, removeTrafficLayer } = useTrafficLayer(map, isMapLoaded)

// 使用路线管理
const routeManagement = useRouteManagement(map, moveSymbolLayersToTop, isMapLoaded)
const {
    routeType,
    isUpdatingRoutes,
    clearAllRoutes,
    hideAllRoutes,
    showRoute,
    redrawRoutes,
    drawDirectRoute,
    drawRealisticRoute,  // 添加这个函数
    drawRoutes,
    updateRouteOnOrdersReordered,
    updateRouteSegments,
    handleRouteOrderChange,
    fetchRouteSegment,
    rebuildAndUpdateRouteDisplay
} = routeManagement

// 使用 Symbol 图层拖拽功能
const {
    isDraggingMapSymbol,
    isCurrentlyDragging, // 获取当前是否正在拖拽的计算属性
    initDragHandlers,
    cleanupDragHandlers,
    handleOrdersUpdated: handleDragOrdersUpdated
} = useMapSymbolDragging(map, moveSymbolLayersToTop)

// 使用标记管理
const {
    markers,
    isUpdatingMarkers,
    popupCache,
    activePopup,
    clearMarkers,
    updateMarkers,
    updateMarkersSelectionsFromStore,
    updateMarkerSelection,
    selectedOrders,
    addMarkerLayers,
    addMarkerImages,
    clearOrderSymbols,
    handleOrdersUpdated: markerHandleOrdersUpdated,
    addMarker,
    updateMarker,
    updateOrdersData, // 添加这个函数
    updateSingleOrderMarker // 添加单个订单标记更新函数
} = useMarkerManagement(map, isCurrentlyDragging) // 传递拖拽状态

// 使用司机标记管理
const {
    driverMarkers,
    showDrivers,
    activeDriverPopup,
    updateDriverMarkers,
    toggleDrivers,
    updateDriversPositions,
    onDriverPositionsUpdated,
    addDriversLayer,
    fitMapToDrivers
} = useDriverMarkers(map)

// 使用司机家庭地址标记管理
const {
    showDriverHomes,
    addDriverHomesLayer,
    updateDriverHomeMarkers,
    toggleDriverHomes
} = useDriverHomeMarkers(map, moveSymbolLayersToTop)

// 使用仓库地址标记管理
const {
    showWarehouse,
    addWarehouseLayer,
    updateWarehouseMarker,
    toggleWarehouse
} = useWarehouseMarker(map, moveSymbolLayersToTop)

// 使用地图交互
const {
    isHandlingMapEvent,
    isHandlingListEvent,
    lastClickTime,
    clickDebounceTime,
    isDrawingSelectionBox,
    selectionBox,
    selectionStart,
    selectionCurrent,
    isCtrlPressed,
    isShiftPressed,
    showSelectionTooltip,
    selectionTooltipContent,
    selectedOrders: mapSelectedOrders,
    bindMapEventHandlers,
    unbindMapEventHandlers
} = useMapInteractions(map, mapContainer)

// 使用地理编码功能
const {
    isProcessingCoordinates,
    processingProgress,
    processingStats,
    updateMapWithValidCoordinates,
    geocodingCacheSize
} = useGeocoding({
    map,
    updateMarkers
})

// 保存路线按钮状态
const showSaveRouteButton = ref(false)
const isSavingRoute = ref(false)
const currentRouteToSave = ref(null)

// 提供地图路线管理功能给其他组件
const mapRouteManagementAPI = {
    updateRouteSegments,
    handleRouteOrderChange,
    fetchRouteSegment,
    rebuildAndUpdateRouteDisplay,
    clearAllRoutes,
    hideAllRoutes,
    showRoute,
    redrawRoutes,
    drawDirectRoute,
    drawRealisticRoute,  // 添加这个关键函数
    drawRoutes
}

provide('mapRouteManagement', mapRouteManagementAPI)

// 暴露到 window 对象供地图拖拽使用
if (typeof window !== 'undefined') {
    window.mapRouteManagement = mapRouteManagementAPI
    window.updateOrdersData = updateOrdersData // 暴露订单数据更新函数
    window.updateSingleOrderMarker = updateSingleOrderMarker // 暴露单个订单标记更新函数
}

// 暴露方法给父组件
defineExpose({
    updateRouteSegments,
    handleRouteOrderChange,
    fetchRouteSegment,
    rebuildAndUpdateRouteDisplay,
    clearAllRoutes,
    hideAllRoutes,
    showRoute,
    redrawRoutes,
    drawDirectRoute,
    drawRealisticRoute,
    drawRoutes
})

// 计算当前显示的订单
const currentOrders = computed(() => {
    console.log('计算当前订单 (MapViewNew):', {
        filteredOrders: window.filteredOrdersFromList,
        selectedRoute: routeStore.selectedRoute,
        selectedDriver: driverStore.selectedDriver,
        selectedOrder: orderStore.selectedOrder,
        selectedOrders: orderStore.selectedOrders?.length,
        allOrders: orderStore.allOrders?.length,
        showAllOrders: window.showAllOrders
    })

    // 如果有过滤的订单列表，优先使用
    if (window.filteredOrdersFromList && Array.isArray(window.filteredOrdersFromList)) {
        return window.filteredOrdersFromList
    }

    // 根据不同条件过滤订单
    if (routeStore.selectedRoute) {
        return orderStore.getOrdersByRouteNumber(routeStore.selectedRoute.id)
    } else if (driverStore.selectedDriver) {
        return orderStore.getDriverOrders(driverStore.selectedDriver.id)
    }
    // 当有选中的订单时，仍然保持与订单列表的过滤结果一致
    // 如果没有过滤结果，则根据showAllOrders决定显示所有订单还是只显示未分配订单
    else {
        // 先检查是否有之前的过滤结果
        if (window.lastFilteredOrdersFromList && Array.isArray(window.lastFilteredOrdersFromList)) {
            return window.lastFilteredOrdersFromList
        }

        // 检查全局显示模式标志
        if (window.showAllOrders === false) {
            // 仅显示未分配订单
            return orderStore.orders
        }

        // 显示所有订单
        return orderStore.allOrders
    }
})

// 监听showAllOrders变化，确保模式切换时地图即时更新 (类似旧代码)
watch(() => window.showAllOrders, (newValue) => {
    console.log('显示模式变化 (watch)，更新地图显示:', newValue ? '显示所有订单' : '仅显示未分配订单')

    // 强制更新过滤键 (保留这个逻辑可能有助于调试或触发其他依赖)
    ordersFilterKey.value = Date.now().toString()

    if (map.value && isMapLoaded.value) {
        // 延迟执行，确保 OrderList 组件已经更新了 window.filteredOrdersFromList
        setTimeout(() => {
            requestAnimationFrame(() => {
                // 优先使用 window.filteredOrdersFromList，如果不存在则根据显示模式选择
                let ordersToDisplay;
                if (window.filteredOrdersFromList && Array.isArray(window.filteredOrdersFromList)) {
                    ordersToDisplay = window.filteredOrdersFromList;
                    console.log('使用 window.filteredOrdersFromList，数量:', ordersToDisplay.length);
                } else {
                    // 手动应用过滤逻辑
                    const baseOrders = newValue ? orderStore.allOrders : orderStore.orders;
                    const hideClosedExceptionalOrders = window.hideClosedExceptionalOrders !== false; // 默认为 true

                    if (hideClosedExceptionalOrders) {
                        if (newValue) {
                            // 全部模式：过滤掉未分配的 closed、exceptional 和已取货的取货订单
                            ordersToDisplay = baseOrders.filter(order =>
                                order.driver_id || !(order.status === 'closed' || order.status === 'exceptional' ||
                                                   (order.type === 'PICKUP' && order.status === 'pickedUp'))
                            );
                        } else {
                            // 未分配模式：过滤掉 closed、exceptional 和已取货的取货订单
                            ordersToDisplay = baseOrders.filter(order =>
                                !(order.status === 'closed' || order.status === 'exceptional' ||
                                  (order.type === 'PICKUP' && order.status === 'pickedUp'))
                            );
                        }
                        console.log(`手动应用过滤逻辑，从 ${baseOrders.length} 个订单过滤到 ${ordersToDisplay.length} 个`);
                    } else {
                        ordersToDisplay = baseOrders;
                        console.log('不应用隐藏过滤，使用 store 数据，数量:', ordersToDisplay?.length || 0);
                    }

                    // 更新全局变量
                    window.filteredOrdersFromList = ordersToDisplay;
                }

                console.log('显示模式变化 (watch)，更新订单显示，数量:', ordersToDisplay?.length || 0)
                updateMarkers(ordersToDisplay || []) // 确保传入数组

                // 当切换到"未分配"视图时，隐藏所有路线
                if (!newValue) {
                    console.log('切换到"未分配"视图，隐藏所有路线')
                    hideAllRoutes()
                } else {
                    // 当切换回"全部"视图时，重新绘制路线
                    console.log('切换回"全部"视图，重新绘制路线')
                    redrawRoutes()
                }

                // 调整地图视图（如果需要）
                const validOrders = (ordersToDisplay || []).filter(order => order && (order.lng_lat || order.location))
                if (validOrders.length > 0 && autoZoom.value) {
                     fitMapToOrders(validOrders)
                }
            })
        }, 100) // 100ms 延迟，确保 OrderList 组件先更新
    }
}, { immediate: true })

// 监听订单选中状态变化的监听器 (类似旧代码)
watch(() => orderStore.selectedOrderIds, (selectedIds) => {
    console.log('订单选中状态变化 (watch):', Array.from(selectedIds))

    // 防止无限循环
    if (isHandlingListEvent.value) {
        console.log('订单选中状态变化 (watch) - 被 isHandlingListEvent 阻止');
        return
    }

    try {
        isHandlingListEvent.value = true // 假设 isHandlingListEvent 在 useMapInteractions 中定义和管理
        console.log('订单选中状态变化 (watch) - 更新标记');
        // 更新地图上标记的选择状态
        updateMarkers(currentOrders.value || []) // 重新运行 updateMarkers 会使用最新的 selectedIds
    } finally {
        setTimeout(() => {
             console.log('订单选中状态变化 (watch) - 重置 isHandlingListEvent');
            isHandlingListEvent.value = false
        }, 100)
    }
}, { deep: true })

// 监听司机变化
watch(() => [
    driverStore.drivers,
    showDrivers.value
], debounce(() => {
    if (isMapLoaded.value && isVisible.value && showDrivers.value) {
        updateDriverMarkers()
    }
}, 1000), { deep: true })

// 监听司机变化，更新家庭地址标记
watch(() => [
    driverStore.drivers,
    showDriverHomes.value
], debounce(() => {
    if (isMapLoaded.value && isVisible.value && showDriverHomes.value) {
        updateDriverHomeMarkers()
    }
}, 1000), { deep: true })

// 监听地址存储变化，更新仓库地址标记
watch(() => [
    addressStore.warehouse,
    showWarehouse.value
], debounce(() => {
    if (isMapLoaded.value && isVisible.value && showWarehouse.value) {
        updateWarehouseMarker()
    }
}, 1000), { deep: true })

// 监听路线类型变化
watch(() => routeType.value, (newType, oldType) => {
    if (isMapLoaded.value && isVisible.value) {
        // 使用 nextTick 确保在 DOM 更新后再执行，避免无限循环
        nextTick(() => {
            console.log(`路线类型从 ${oldType} 变为 ${newType}`)

            // 如果是从直线路线切换到真实路线，需要清除现有路线并重新请求
            if (oldType === 'direct' && newType === 'realistic') {
                console.log('从直线路线切换到真实路线，清除现有路线并重新请求')

                // 清除所有路线
                clearAllRoutes()

                // 重新绘制路线
                if (routeStore.selectedRoute) {
                    console.log(`重新绘制选中的路线: ${routeStore.selectedRoute.id}`)
                    const routeId = routeStore.selectedRoute.id
                    const routeOrders = orderStore.getOrdersByRouteNumber(routeId)
                    const validOrders = routeOrders.filter(o => o && (o.lng_lat || o.location))

                    if (validOrders.length > 0) {
                        drawRoutes(validOrders, routeId)
                    }
                } else if (driverStore.selectedDriver) {
                    console.log(`重新绘制选中司机的路线: ${driverStore.selectedDriver.id}`)
                    const driverId = driverStore.selectedDriver.id

                    // 获取该司机的所有路线
                    const driverRoutes = routeStore.routes.filter(route => {
                        return route.driverId === driverId || route.driver === driverId
                    })

                    if (driverRoutes.length === 0) {
                        redrawRoutes()
                    } else {
                        driverRoutes.forEach(route => {
                            const routeId = route.id
                            const routeOrders = orderStore.getOrdersByRouteNumber(routeId)
                            const validOrders = routeOrders.filter(o => o && (o.lng_lat || o.location))

                            if (validOrders.length > 0) {
                                drawRoutes(validOrders, routeId)
                            }
                        })
                    }
                } else {
                    redrawRoutes()
                }
            } else {
                // 对于其他类型的变化，只需隐藏所有路线并重新显示需要的路线
                hideAllRoutes()

                // 如果选中了路线，只显示该路线
                if (routeStore.selectedRoute) {
                    console.log(`路线类型变化，显示选中的路线: ${routeStore.selectedRoute.id}`)
                    // 重新绘制选中的路线
                    const routeId = routeStore.selectedRoute.id
                    const routeOrders = orderStore.getOrdersByRouteNumber(routeId)
                    const validOrders = routeOrders.filter(o => o && (o.lng_lat || o.location))

                    if (validOrders.length > 0) {
                        drawRoutes(validOrders, routeId)
                    }
                }
                // 如果选中了司机，显示该司机的所有路线
                else if (driverStore.selectedDriver) {
                    console.log(`路线类型变化，显示选中司机的路线: ${driverStore.selectedDriver.id}`)
                    const driverId = driverStore.selectedDriver.id

                    // 获取该司机的所有路线
                    const driverRoutes = routeStore.routes.filter(route => {
                        return route.driverId === driverId || route.driver === driverId
                    })

                    if (driverRoutes.length === 0) {
                        // 如果没有找到该司机的路线，重新绘制所有路线
                        redrawRoutes()
                    } else {
                        // 重新绘制该司机的所有路线
                        driverRoutes.forEach(route => {
                            const routeId = route.id
                            const routeOrders = orderStore.getOrdersByRouteNumber(routeId)
                            const validOrders = routeOrders.filter(o => o && (o.lng_lat || o.location))

                            if (validOrders.length > 0) {
                                drawRoutes(validOrders, routeId)
                            }
                        })
                    }
                }
                // 如果没有选中路线和司机，显示所有路线
                else {
                    console.log('路线类型变化，显示所有路线')
                    redrawRoutes()
                }
            }
        })
    }
})

// 监听选中的司机和路线变化
watch(() => [routeStore.selectedRoute, driverStore.selectedDriver], () => {
    if (isMapLoaded.value && isVisible.value) {
        // 使用 nextTick 确保在 DOM 更新后再执行，避免无限循环
        nextTick(() => {
            // 先隐藏所有路线
            hideAllRoutes()

            // 如果选中了路线，只显示该路线
            if (routeStore.selectedRoute) {
                showRoute(routeStore.selectedRoute.id)
            }
            // 如果选中了司机，显示该司机的所有路线
            else if (driverStore.selectedDriver) {
                const driverId = driverStore.selectedDriver.id
                console.log(`选中司机 ${driverId}，查找该司机的路线`)

                // 获取该司机的所有路线
                const driverRoutes = routeStore.routes.filter(route => {
                    const match = route.driverId === driverId || route.driver === driverId
                    if (match) {
                        console.log(`找到司机 ${driverId} 的路线: ${route.id}`)
                    }
                    return match
                })

                console.log(`司机 ${driverId} 共有 ${driverRoutes.length} 条路线`)

                if (driverRoutes.length === 0) {
                    // 如果没有找到该司机的路线，可能是因为路线数据中的司机ID格式不同
                    // 重新绘制所有路线，让用户至少能看到一些内容
                    console.log(`没有找到司机 ${driverId} 的路线，重新绘制所有路线`)
                    redrawRoutes()
                } else {
                    // 显示该司机的所有路线
                    driverRoutes.forEach(route => {
                        showRoute(route.id)
                    })
                }
            }
            // 如果没有选中路线和司机，显示所有路线
            else {
                redrawRoutes()
            }
        })
    }
}, { deep: true })

// 自动缩放功能
watch(() => [
    autoZoom.value,
    currentOrders.value
], debounce(() => {
    if (autoZoom.value && isMapLoaded.value && isVisible.value && currentOrders.value.length > 0) {
        fitMapToOrders(currentOrders.value)
    }
}, 500), { deep: true })

// 监听订单状态变化
watch(() => orderStore.allOrders.filter(o =>
  o.status === 'pickedUp' || o.status === 'delivered'
), (statusChangedOrders, oldStatusChangedOrders) => {
  if (!map.value || !isMapLoaded.value) return;

  // 找出状态发生变化的订单
  const changedOrders = [];

  statusChangedOrders.forEach(newOrder => {
    const oldOrder = oldStatusChangedOrders?.find(o => o.id === newOrder.id);
    if (!oldOrder || oldOrder.status !== newOrder.status) {
      changedOrders.push(newOrder);
    }
  });

  if (changedOrders.length > 0) {
    console.log(`[Vue Reactivity] 检测到 ${changedOrders.length} 个订单状态变化，更新标记`);

    // 更新这些订单的标记
    changedOrders.forEach(order => {
      if (typeof updateMarker === 'function') {
        updateMarker(order, true); // 强制更新
        console.log(`[Vue Reactivity] 更新订单标记: ${order.id}, 状态: ${order.status}, 类型: ${order.type}`);
      }
    });
  }
}, { deep: true })

// 监听订单创建和更新
watch(() => orderStore.allOrders, (newOrders, oldOrders) => {
  if (!map.value || !isMapLoaded.value) return;

  // 如果是初始加载，不处理
  if (!oldOrders || oldOrders.length === 0) return;

  // 找出新增的订单
  const addedOrders = newOrders.filter(newOrder =>
    !oldOrders.some(oldOrder => oldOrder.id === newOrder.id)
  );

  // 找出更新的订单（不包括状态变化的订单，那些由上面的watch处理）
  const updatedOrders = newOrders.filter(newOrder => {
    const oldOrder = oldOrders.find(o => o.id === newOrder.id);
    return oldOrder &&
           oldOrder.status !== 'pickedUp' &&
           oldOrder.status !== 'delivered' &&
           JSON.stringify(newOrder) !== JSON.stringify(oldOrder);
  });

  // 处理新增的订单
  if (addedOrders.length > 0) {
    console.log(`检测到 ${addedOrders.length} 个新增订单，添加标记`);

    // 检查这些订单是否应该显示在当前视图中
    const ordersToDisplay = addedOrders.filter(order => {
      // 如果选中了路线，只显示该路线的订单
      if (routeStore.selectedRoute) {
        return order.route_id === routeStore.selectedRoute.id;
      }
      // 如果选中了司机，只显示该司机的订单
      else if (driverStore.selectedDriver) {
        return order.driver_id === driverStore.selectedDriver.id;
      }
      // 否则根据当前显示模式决定
      else {
        return window.showAllOrders ? true : !order.route_id;
      }
    });

    // 添加这些订单的标记
    ordersToDisplay.forEach(order => {
      if (typeof addMarker === 'function') {
        addMarker(order);
        console.log(`添加新订单标记: ${order.id}`);
      }
    });
  }

  // 处理更新的订单
  if (updatedOrders.length > 0) {
    console.log(`检测到 ${updatedOrders.length} 个更新订单，更新标记`);

    // 更新这些订单的标记
    updatedOrders.forEach(order => {
      if (typeof updateMarker === 'function') {
        updateMarker(order);
        console.log(`更新订单标记: ${order.id}`);
      }
    });
  }
}, { deep: true })

// 组件初始化
onMounted(() => {
    console.log('MapView组件挂载')
    initializeMap(() => {
        // 地图加载完成后初始化各种功能
        bindMapEventHandlers()
        addTrafficLayer()
        addMarkerLayers()
        addMarkerImages()

        // 初始化 Symbol 图层拖拽功能
        initDragHandlers()

        // 监听订单更新事件
        eventBus.on(EVENT_TYPES.ORDERS_UPDATED, handleDragOrdersUpdated)

        // 强制获取最新订单数据
        console.log('地图加载完成，强制更新最新订单数据');

        // 强制清除订单缓存
        orderStore.invalidateCache();

        // 获取当前显示模式下的订单，应用正确的过滤逻辑
        let initialOrders = [];

        if (window.filteredOrdersFromList && Array.isArray(window.filteredOrdersFromList) && window.filteredOrdersFromList.length > 0) {
            // 如果 OrderList 组件已经设置了过滤结果，优先使用
            initialOrders = window.filteredOrdersFromList;
            console.log('使用 OrderList 组件的过滤结果，订单数量:', initialOrders.length);
        } else {
            console.log('OrderList 组件的过滤结果还未准备好，使用地图组件的过滤逻辑');
            // 否则根据当前设置手动应用过滤逻辑
            // 安全地处理 window.showAllOrders，默认为 false（未分配模式）
            const showAllOrders = window.showAllOrders === true;
            const hideClosedExceptionalOrders = window.hideClosedExceptionalOrders !== false; // 默认为 true

            console.log('地图初始化时的显示模式:', showAllOrders ? '全部订单' : '未分配订单');
            console.log('地图初始化时的隐藏模式:', hideClosedExceptionalOrders ? '隐藏特殊状态' : '显示全部状态');

            const baseOrders = showAllOrders ? orderStore.allOrders : orderStore.orders;

            if (hideClosedExceptionalOrders) {
                if (showAllOrders) {
                    // 全部模式：过滤掉未分配的 closed、exceptional 和已取货的取货订单
                    initialOrders = baseOrders.filter(order =>
                        order.driver_id || !(order.status === 'closed' || order.status === 'exceptional' ||
                                           (order.type === 'PICKUP' && order.status === 'pickedUp'))
                    );
                } else {
                    // 未分配模式：过滤掉 closed、exceptional 和已取货的取货订单
                    initialOrders = baseOrders.filter(order =>
                        !(order.status === 'closed' || order.status === 'exceptional' ||
                          (order.type === 'PICKUP' && order.status === 'pickedUp'))
                    );
                }
                console.log(`应用隐藏过滤逻辑，从 ${baseOrders.length} 个订单过滤到 ${initialOrders.length} 个`);
            } else {
                initialOrders = baseOrders;
                console.log('不应用隐藏过滤，使用全部订单:', initialOrders.length);
            }

            // 更新全局变量，确保其他组件能获取到正确的过滤结果
            window.showAllOrders = showAllOrders;
            window.hideClosedExceptionalOrders = hideClosedExceptionalOrders;
            window.filteredOrdersFromList = initialOrders;
        }

        console.log(`初始化地图，加载${initialOrders.length}个订单`);
        updateMapWithValidCoordinates(initialOrders || []);

        if (showDrivers.value) {
            addDriversLayer() // 确保司机图层已添加
            updateDriverMarkers()
        }

        // 添加司机家庭地址图层
        if (showDriverHomes.value) {
            addDriverHomesLayer() // 确保司机家庭地址图层已添加
            updateDriverHomeMarkers()
        }

        // 添加仓库地址图层
        if (showWarehouse.value) {
            addWarehouseLayer() // 确保仓库地址图层已添加
            updateWarehouseMarker()
        }

        // 根据当前显示模式决定是否绘制路线
        // 使用安全的默认值检查
        const showAllOrders = window.showAllOrders === true;
        if (showAllOrders) {
            console.log('初始状态为"全部"视图，绘制路线')
            redrawRoutes()
        } else {
            console.log('初始状态为"未分配"视图，不绘制路线')
            hideAllRoutes()
        }

        // 订阅事件总线上的事件 (确保使用上面定义的处理器)
        eventBus.off(EVENT_TYPES.ORDERS_FILTERED, handleOrdersFiltered)
        eventBus.off(EVENT_TYPES.ORDER_SELECTED, handleOrderSelectionChanged)
        eventBus.off(EVENT_TYPES.ORDERS_BATCH_SELECTED, handleBatchOrderSelectionChanged)
        eventBus.off(EVENT_TYPES.ORDERS_SELECTION_CLEARED, handleOrdersSelectionCleared)
        // 其他事件订阅...

        eventBus.on(EVENT_TYPES.ORDERS_FILTERED, handleOrdersFiltered)
        eventBus.on(EVENT_TYPES.ORDER_SELECTED, handleOrderSelectionChanged)
        eventBus.on(EVENT_TYPES.ORDERS_BATCH_SELECTED, handleBatchOrderSelectionChanged)
        eventBus.on(EVENT_TYPES.ORDERS_SELECTION_CLEARED, handleOrdersSelectionCleared)

        // 添加对订单更新事件的处理
        eventBus.off(EVENT_TYPES.ORDERS_UPDATED, handleOrdersUpdated)
        eventBus.on(EVENT_TYPES.ORDERS_UPDATED, handleOrdersUpdated)
        console.log('已注册订单更新事件监听器', EVENT_TYPES.ORDERS_UPDATED)

        // 添加对单个订单更新事件的处理
        eventBus.off(EVENT_TYPES.ORDER_UPDATED, handleOrdersUpdated)
        eventBus.on(EVENT_TYPES.ORDER_UPDATED, handleOrdersUpdated)
        console.log('已注册单个订单更新事件监听器', EVENT_TYPES.ORDER_UPDATED)

        // 添加对内部订单更新事件的处理
        eventBus.off('internal:orders-updated', handleOrdersUpdated)
        eventBus.on('internal:orders-updated', handleOrdersUpdated)
        console.log('已注册内部订单更新事件监听器', 'internal:orders-updated')

        // 添加对订单添加事件的处理
        eventBus.off(EVENT_TYPES.ORDER_ADDED, handleOrderAddedIncremental)
        eventBus.on(EVENT_TYPES.ORDER_ADDED, handleOrderAddedIncremental)
        console.log('已注册订单添加事件监听器', EVENT_TYPES.ORDER_ADDED)

        // 添加对地图刷新事件的处理
        eventBus.off(EVENT_TYPES.MAP_REFRESH, handleMapRefresh)
        eventBus.on(EVENT_TYPES.MAP_REFRESH, handleMapRefresh)
        console.log('已注册地图刷新事件监听器', EVENT_TYPES.MAP_REFRESH)

        // 添加对订单取消分配事件的处理
        eventBus.off(EVENT_TYPES.ORDERS_UNASSIGNED, handleOrdersUnassigned)
        eventBus.on(EVENT_TYPES.ORDERS_UNASSIGNED, handleOrdersUnassigned)
        console.log('已注册订单取消分配事件监听器', EVENT_TYPES.ORDERS_UNASSIGNED)

        // 订阅司机位置更新事件
        eventBus.off(EVENT_TYPES.DRIVER_POSITIONS_UPDATED, onDriverPositionsUpdated)
        eventBus.on(EVENT_TYPES.DRIVER_POSITIONS_UPDATED, onDriverPositionsUpdated)
        console.log('已注册司机位置更新事件监听器', EVENT_TYPES.DRIVER_POSITIONS_UPDATED)

        // 清除旧的监听器
        eventBus.off(EVENT_TYPES.DRIVER_POSITIONS_UPDATED, onDriverPositionsUpdated);

        // 添加监听器
        eventBus.on(EVENT_TYPES.DRIVER_POSITIONS_UPDATED, onDriverPositionsUpdated)
        console.log('已注册司机位置更新事件监听器', EVENT_TYPES.DRIVER_POSITIONS_UPDATED)

        // 直接请求一次司机位置，确保地图初始化后显示
        if (driverStore.allDriverPositions && driverStore.allDriverPositions.length > 0) {
            console.log('地图初始化后请求初始司机位置');
            updateDriversPositions(driverStore.allDriverPositions, autoZoom.value);
            // 记录初始更新时间
            window.__lastDriverPositionUpdate = Date.now();
        }
    })
})

// 组件销毁
onBeforeUnmount(() => {
    console.log('MapView组件将要卸载')

    // 解绑事件处理
    unbindMapEventHandlers()

    // 取消事件总线订阅 (确保所有订阅都被取消)
    eventBus.off(EVENT_TYPES.ORDERS_FILTERED, handleOrdersFiltered)
    eventBus.off(EVENT_TYPES.ORDER_SELECTED, handleOrderSelectionChanged)
    eventBus.off(EVENT_TYPES.ORDERS_BATCH_SELECTED, handleBatchOrderSelectionChanged)
    eventBus.off(EVENT_TYPES.ORDERS_SELECTION_CLEARED, handleOrdersSelectionCleared)
    eventBus.off(EVENT_TYPES.ORDERS_UPDATED, handleOrdersUpdated)
    eventBus.off(EVENT_TYPES.ORDER_UPDATED, handleOrdersUpdated)
    eventBus.off('internal:orders-updated', handleOrdersUpdated)
    eventBus.off(EVENT_TYPES.ORDER_ADDED, handleOrderAddedIncremental)
    eventBus.off(EVENT_TYPES.MAP_REFRESH, handleMapRefresh)
    eventBus.off(EVENT_TYPES.ORDERS_UNASSIGNED, handleOrdersUnassigned)
    eventBus.off(EVENT_TYPES.DRIVER_POSITIONS_UPDATED, onDriverPositionsUpdated)

    // 清理定时器
    if (window.driverPositionCheckTimer) {
        clearInterval(window.driverPositionCheckTimer);
        delete window.driverPositionCheckTimer;
    }
})

onUnmounted(() => {
    console.log('MapView组件已卸载')

    // 移除事件监听
    eventBus.off(EVENT_TYPES.ORDERS_UPDATED, handleDragOrdersUpdated)

    // 清理拖拽处理器
    cleanupDragHandlers()

    // 清理资源
    removeTrafficLayer()
    clearAllRoutes()

    // 清除标记
    try {
        // 使用 clearCustomIcons 函数清除自定义图标
        if (typeof clearCustomIcons === 'function') {
            clearCustomIcons();
        }

        // 如果有 clearOrderSymbols 函数，使用它清除订单标记
        if (typeof clearOrderSymbols === 'function') {
            clearOrderSymbols();
        }

        // 隐藏任何活动的弹窗
        if (typeof hideOrderPopup === 'function') {
            hideOrderPopup();
        }
    } catch (error) {
        console.error('清除标记时出错:', error);
    }

    removeMap()
})

// 根据需求将地图缩放适配所有订单
const fitMapToOrders = (orders) => {
    if (!map.value || !orders || orders.length === 0) return

    try {
        // 收集所有有效的订单位置
        const validPoints = orders.filter(order => {
            return order.lng_lat && Array.isArray(order.lng_lat) && order.lng_lat.length === 2
        }).map(order => {
            // 注意：我们的坐标是 [lat, lng] 格式，而 MapLibre 需要 [lng, lat] 格式
            const [lat, lng] = order.lng_lat;
            return [lng, lat]; // 转换为MapLibre需要的[lng, lat]格式
        })

        if (validPoints.length === 0) return

        // 添加调试日志
        console.log('有效坐标点数量:', validPoints.length);
        console.log('第一个坐标点:', validPoints[0]);

        // 只有一个点时，直接缩放到该点
        if (validPoints.length === 1) {
            map.value.flyTo({
                center: validPoints[0],
                zoom: 14,
                duration: 1000
            })
            return
        }

        // 创建边界
        const bounds = validPoints.reduce((bounds, point) => {
            return bounds.extend(point)
        }, new maplibregl.LngLatBounds(validPoints[0], validPoints[0]))

        // 打印边界信息
        console.log('计算的边界范围:', bounds.toString());

        // 缩放到边界
        map.value.fitBounds(bounds, {
            padding: 50,
            maxZoom: 16,
            duration: 1000
        })
    } catch (error) {
        console.error('自动缩放地图时出错:', error)
    }
}

// 更新处理订单过滤事件
const handleOrdersFiltered = (eventData) => {
    console.log('MapViewNew 收到订单过滤事件:', eventData)
    // 过滤事件通常意味着需要完全重绘当前可见的标记
    if (map.value && isMapLoaded.value) {
        // updateMapWithValidCoordinates 内部应该处理清空和重绘逻辑
        const ordersToDisplay = eventData.orders || [] // 使用事件传递过来的过滤后订单
        console.log('订单过滤事件详情:', {
            filteredCount: ordersToDisplay.length,
            filterCriteria: eventData.filterCriteria,
            showAllOrders: eventData.filterCriteria?.showAllOrders // 从过滤条件中获取显示模式
        })

        // 检查是否是日期或班次变化
        const isDateChange = eventData.filterCriteria?.isDateChange === true
        const isShiftChange = eventData.filterCriteria?.isShiftChange === true
        const isInitialLoad = eventData.filterCriteria?.isInitialLoad === true
        const showAllOrders = eventData.filterCriteria?.showAllOrders === true

        console.log('过滤事件详细信息:', {
            isDateChange,
            isShiftChange,
            isInitialLoad,
            showAllOrders,
            ordersCount: ordersToDisplay.length
        })

        // 如果是日期或班次变化，清空之前的路线
        if (isDateChange || isShiftChange) {
            console.log(`检测到${isDateChange ? '日期' : '班次'}变化，清空之前的路线`)
            // 清除所有路线图层和源
            clearAllRoutes()

            // 清除路线选择
            if (routeStore.selectedRoute) {
                routeStore.setSelectedRoute(null)
            }

            // 清除司机选择
            if (driverStore.selectedDriver) {
                driverStore.setSelectedDriver(null)
            }

            // 在短暂延迟后根据当前显示模式决定是否绘制路线
            setTimeout(() => {
                // 检查当前显示模式
                if (!showAllOrders) {
                    // 如果是"未分配"视图，不绘制路线
                    console.log('当前是"未分配"视图，不绘制路线')
                    hideAllRoutes()
                } else {
                    // 如果是"全部"视图，绘制路线
                    console.log('当前是"全部"视图，绘制新班次的路线')
                    // 检查新班次是否有路线
                    if (routeStore.routes && routeStore.routes.length > 0) {
                        console.log(`新班次有 ${routeStore.routes.length} 条路线，开始绘制`)
                        redrawRoutes()
                    } else {
                        console.log('新班次没有路线，不需要绘制')
                    }
                }
            }, 500) // 给路线数据加载一些时间
        } else if (isInitialLoad) {
            // 如果是初始加载，根据显示模式决定是否绘制路线
            console.log('检测到初始加载事件，根据显示模式决定路线显示')
            if (!showAllOrders) {
                // 初始加载时是"未分配"视图，不绘制路线
                console.log('初始加载：未分配视图，隐藏所有路线')
                hideAllRoutes()
            } else {
                // 初始加载时是"全部"视图，绘制路线
                console.log('初始加载：全部视图，绘制路线')
                redrawRoutes()
            }
        } else {
            // 如果是显示模式变化（未分配/全部），处理路线显示
            if (eventData.filterCriteria && 'showAllOrders' in eventData.filterCriteria) {
                if (!showAllOrders) {
                    // 切换到"未分配"视图，隐藏所有路线
                    console.log('过滤事件：切换到"未分配"视图，隐藏所有路线')
                    hideAllRoutes()
                } else {
                    // 切换回"全部"视图，重新绘制路线
                    console.log('过滤事件：切换回"全部"视图，重新绘制路线')
                    redrawRoutes()
                }
            }
        }

        // 更新全局变量，确保 currentOrders 计算正确 (如果其他地方还在用)
        window.filteredOrdersFromList = ordersToDisplay
        window.showAllOrders = showAllOrders

        // 触发地图更新，使用地理编码处理
        updateMapWithValidCoordinates(ordersToDisplay)

        // 根据过滤条件调整地图视野 (可选)
        if (autoZoom.value && ordersToDisplay.length > 0) {
            fitMapToBounds(ordersToDisplay); // 需要实现 fitMapToBounds
        } else if (ordersToDisplay.length === 0) {
            // 没有订单时，可以重置视图或显示默认区域
            // map.value.flyTo({ center: defaultCenter, zoom: defaultZoom });
        }
    }
}

// 处理订单选择事件
const handleOrderSelectionChanged = (data) => {
    if (isHandlingMapEvent.value) {
        console.log('处理订单选择变化事件 - 被 isHandlingMapEvent 阻止', data?.source);
        return;
    }
    console.log('处理订单选择变化事件 - 更新标记', data?.source);
    try {
        isHandlingListEvent.value = true; // 标记正在处理列表事件
        updateMarkers(currentOrders.value || []); // 直接调用更新
    } finally {
        setTimeout(() => {
            isHandlingListEvent.value = false;
        }, 100);
    }
}

// 处理批量订单选择事件
const handleBatchOrderSelectionChanged = (data) => {
    if (isHandlingMapEvent.value) {
        console.log('处理批量订单选择事件 - 被 isHandlingMapEvent 阻止', data?.source);
        return;
    }
    console.log('处理批量订单选择事件 - 更新标记', data?.source);
    try {
        isHandlingListEvent.value = true;
        updateMarkers(currentOrders.value || []);
    } finally {
        setTimeout(() => {
            isHandlingListEvent.value = false;
        }, 100);
    }
}

// 处理订单选择清除事件
const handleOrdersSelectionCleared = (data) => {
    if (isHandlingMapEvent.value) {
         console.log('处理订单选择清除事件 - 被 isHandlingMapEvent 阻止', data?.source);
        return;
    }
     console.log('处理订单选择清除事件 - 更新标记', data?.source);
    try {
        isHandlingListEvent.value = true;
        updateMarkers(currentOrders.value || []);
    } finally {
        setTimeout(() => {
            isHandlingListEvent.value = false;
        }, 100);
    }
}

// 处理地图刷新事件
const handleMapRefresh = (data) => {
    console.log('收到地图刷新事件:', data);

    if (!map.value || !isMapLoaded.value) {
        console.warn('地图未加载，无法刷新');
        return;
    }

    // 检查是否是拖拽操作，如果是，显示保存按钮
    if (data && data.isDragging) {
        console.log('检测到拖拽操作，显示保存按钮');
        showSaveRouteButton.value = true;
        currentRouteToSave.value = {
            id: data.routeId,
            name: data.routeName,
            orders: data.orders
        };
    }

    // 获取当前显示的订单
    let ordersToDisplay = [];

    // 优先使用传入的订单数据
    if (data && data.orders && Array.isArray(data.orders) && data.orders.length > 0) {
        ordersToDisplay = data.orders;

        console.log('使用传入的订单数据更新地图，数量:', ordersToDisplay.length);

        // 直接更新传入的订单数据，不清除其他标记
        // 这样可以避免闪烁问题
        try {
            // 获取受影响的订单ID列表
            const affectedOrderIds = data.affectedOrderIds || [];

            // 检查是否有受影响的订单ID列表
            if (affectedOrderIds.length > 0) {
                console.log('检测到受影响的订单ID列表，优先更新这些订单的标记');

                // 创建一个集合，用于快速查找受影响的订单ID
                const affectedOrderIdSet = new Set(affectedOrderIds);

                // 遍历所有订单，优先更新受影响的订单
                ordersToDisplay.forEach(order => {
                    if (typeof updateMarker === 'function') {
                        if (affectedOrderIdSet.has(order.id)) {
                            // 如果是受影响的订单，强制更新其标记
                            console.log(`强制更新受影响的订单标记: ${order.id}, 停靠点编号: ${order.stop_no}`);
                            updateMarker(order, true); // 传递 true 表示强制更新
                        } else {
                            // 如果不是受影响的订单，正常更新
                            updateMarker(order);
                        }
                    }
                });
            } else {
                // 如果没有受影响的订单ID列表，正常更新所有订单
                console.log('没有受影响的订单ID列表，正常更新所有订单');
                ordersToDisplay.forEach(order => {
                    if (typeof updateMarker === 'function') {
                        updateMarker(order);
                    }
                });
            }

            // 如果是路线更新，重绘路线
            if (data.routeId) {
                // 检查是否需要重新绘制路线（例如，来自HERE优化或地图拖拽）
                if (data.needRedrawRoute || data.isHereOptimize || data.isMapDrag) {
                    console.log(`检测到需要重新绘制路线 ${data.routeId}，原因:`,
                        data.needRedrawRoute ? 'needRedrawRoute标志' :
                        data.isHereOptimize ? 'HERE优化' :
                        data.isMapDrag ? '地图拖拽' : '未知');

                    try {
                        // 先清除现有路线
                        clearRouteById(data.routeId);

                        // 使用redrawRoutes重新绘制所有路线，或者只绘制特定路线
                        setTimeout(() => {
                            if (typeof drawRoutes === 'function') {
                                console.log(`使用drawRoutes绘制路线 ${data.routeId}`);
                                drawRoutes(ordersToDisplay, data.routeId);
                            } else {
                                console.log(`使用redrawRoutes重新绘制所有路线`);
                                redrawRoutes();
                            }
                        }, 100);
                    } catch (error) {
                        console.error(`清除和重绘路线 ${data.routeId} 时出错:`, error);
                        // 如果清除失败，尝试使用更新方法
                        console.log(`尝试使用updateRouteOnOrdersReordered更新路线 ${data.routeId}`);
                        updateRouteOnOrdersReordered(data.routeId, ordersToDisplay);
                    }
                } else {
                    // 使用updateRouteOnOrdersReordered方法更新路线
                    console.log(`使用updateRouteOnOrdersReordered更新路线 ${data.routeId}`);
                    updateRouteOnOrdersReordered(data.routeId, ordersToDisplay);
                }
            }
        } catch (error) {
            console.error('更新标记时出错:', error);
        }
    } else {
        // 如果没有传入订单数据，则获取当前显示的订单
        if (routeStore.selectedRoute) {
            ordersToDisplay = orderStore.getOrdersByRouteNumber(routeStore.selectedRoute.id);
        } else if (driverStore.selectedDriver) {
            ordersToDisplay = orderStore.getDriverOrders(driverStore.selectedDriver.id);
        } else {
            ordersToDisplay = window.filteredOrdersFromList ||
                           (window.showAllOrders ? orderStore.allOrders : orderStore.orders);
        }

        console.log('使用当前订单数据更新地图，数量:', ordersToDisplay.length);

        // 检查是否有新添加的订单
        const newOrders = [];
        if (data && data.newOrderIds && Array.isArray(data.newOrderIds) && data.newOrderIds.length > 0) {
            console.log('检测到新添加的订单ID列表:', data.newOrderIds);

            // 查找这些新订单
            data.newOrderIds.forEach(orderId => {
                const order = orderStore.allOrders.find(o => o.id === orderId);
                if (order) {
                    newOrders.push(order);
                    console.log(`找到新添加的订单: ${order.id}`);
                }
            });

            // 如果找到了新订单，单独处理它们
            if (newOrders.length > 0) {
                console.log(`处理 ${newOrders.length} 个新添加的订单`);

                // 添加这些新订单的标记
                newOrders.forEach(order => {
                    if (typeof addMarker === 'function') {
                        addMarker(order);
                        console.log(`添加新订单标记: ${order.id}`);
                    }
                });
            }
        }

        // 使用现有函数更新所有标记，但不清除
        updateMarkers(ordersToDisplay || []);

        // 重新绘制所有路线
        redrawRoutes();
    }
}

// 保存路线顺序
const saveRouteOrder = async () => {
    if (isSavingRoute.value || !currentRouteToSave.value) return;

    try {
        // 设置保存中状态
        isSavingRoute.value = true;

        // 获取当前顺序的订单ID列表
        const orderedOrderIds = currentRouteToSave.value.orders.map(o => o.id);

        console.log('保存路线顺序 - 订单数量:', orderedOrderIds.length);
        console.log('保存路线顺序 - 订单ID列表:', orderedOrderIds);
        console.log('保存路线顺序 - 路线ID:', currentRouteToSave.value.id);
        console.log('保存路线顺序 - 路线名称:', currentRouteToSave.value.name);

        // 调用Store重新排序路线停靠点
        await orderStore.reorderRouteStops(currentRouteToSave.value.id, orderedOrderIds);

        // 再次发送一个明确的更新事件，确保DriverRoutePanel能收到
        console.log('发送明确的ORDERS_UPDATED事件，确保DriverRoutePanel能收到');
        eventBus.emit(EVENT_TYPES.ORDERS_UPDATED, {
            orders: currentRouteToSave.value.orders,
            routeId: currentRouteToSave.value.id,
            routeName: currentRouteToSave.value.name,
            isDragging: false, // 这不是拖拽中的更新，而是最终保存
            isMapSave: true, // 标记这是来自地图的保存操作
            timestamp: Date.now()
        });

        // 提示成功
        ElMessage.success('路线顺序已保存');

        // 隐藏保存按钮
        showSaveRouteButton.value = false;
        currentRouteToSave.value = null;
    } catch (error) {
        console.error('保存路线顺序失败:', error);
        ElMessage.error('保存路线顺序失败');
    } finally {
        // 清除保存中状态
        isSavingRoute.value = false;
    }
}

// 处理订单更新事件
const handleOrdersUpdated = (data) => {
    if (!data) {
        console.warn('收到空的订单更新事件数据')
        return
    }

    console.log('MapViewNew 收到订单更新事件:', data)

    // 优先处理拖拽操作导致的更新
    if (data.isDragOperation) {
        console.log('检测到拖拽操作，优先处理');

        // 如果是强制重置，则清除所有标记并重新创建
        if (data.forceReset) {
            console.log('检测到强制重置标志，清除所有标记并重新创建');

            // 清除所有标记
            clearOrderSymbols();

            // 重新创建标记
            setTimeout(() => {
                // 获取当前显示的订单
                const currentOrdersToDisplay = window.filteredOrdersFromList ||
                                             (window.showAllOrders ? orderStore.allOrders : orderStore.orders);

                // 更新标记
                updateMarkers(currentOrdersToDisplay);
            }, 50);

            return;
        }

        // 直接调用useMarkerManagement中的handleOrdersUpdated方法处理拖拽操作
        markerHandleOrdersUpdated(data);
        return;
    }

    // 使用useMarkerManagement中新增的handleOrdersUpdated方法来处理
    if (data.isAssign || data.isUnassign) {
        console.log('分派markerHandleOrdersUpdated处理分配/取消分配操作');
        markerHandleOrdersUpdated(data);
        return;
    }

    // 处理强制刷新事件
    if (data.isForceRefresh) {
        console.log('收到强制刷新事件，重新获取最新订单数据')

        // 强制清除缓存
        orderStore.invalidateCache()

        // 获取当前显示模式下的订单
        let ordersToDisplay = [];
        if (routeStore.selectedRoute) {
            ordersToDisplay = orderStore.getOrdersByRouteNumber(routeStore.selectedRoute.id);
        } else if (driverStore.selectedDriver) {
            ordersToDisplay = orderStore.getDriverOrders(driverStore.selectedDriver.id);
        } else {
            // 使用全局过滤
            ordersToDisplay = window.filteredOrdersFromList ||
                           (window.showAllOrders ? orderStore.allOrders : orderStore.orders);
        }

        // 如果当前地图已加载，立即刷新显示
        if (map.value && isMapLoaded.value) {
            // 清除当前显示的所有标记
            clearOrderSymbols();

            // 增加延迟确保清除完成并且状态已更新
            setTimeout(() => {
                console.log('强制刷新地图显示，订单数量:', ordersToDisplay.length);

                // 使用地理编码处理更新标记
                updateMapWithValidCoordinates(ordersToDisplay || []);

                // 重新绘制所有路线
                redrawRoutes();

                // 如果需要且有有效的订单坐标，调整地图视图
                if (autoZoom.value && ordersToDisplay.length > 0) {
                    const validOrders = ordersToDisplay.filter(o => o && (o.lng_lat || o.location));
                    if (validOrders.length > 0) {
                        fitMapToOrders(validOrders);
                    }
                }
            }, 1000); // 将延迟时间从300ms增加到1000ms
        }

        return;
    }

    // *** 处理状态变更更新 ***
    if (data.isStatusChange && data.orders && data.orders.length > 0) {
        console.log('MapViewNew 收到订单状态变更事件，调用 useMarkerManagement.updateMarker');
        console.log('状态变更详情:', {
            status: data.status,
            type: data.type,
            timestamp: data.timestamp
        });

        const updatedOrders = data.orders;
        console.log('将更新以下订单的标记 (状态变更):', updatedOrders.map(o => o.id));

        try {
            updatedOrders.forEach(orderToUpdate => {
                if (typeof updateMarker === 'function') {
                    // 使用强制更新确保状态变化反映在地图上
                    updateMarker(orderToUpdate, true);
                    console.log(`已请求更新标记 (状态变更): ${orderToUpdate.id}, 状态: ${orderToUpdate.status}, 类型: ${orderToUpdate.type}`);
                } else {
                    console.error("useMarkerManagement 中的 updateMarker 函数不可用！");
                }
            });
        } catch (error) {
            console.error('调用 updateMarker 处理状态变更时出错:', error);
        }
        return; // 处理完毕
    }

    // *** 处理通用订单更新 ***
    if (data.isGeneralUpdate && data.orders && data.orders.length > 0) {
        console.log('MapViewNew 收到通用订单更新事件，调用 useMarkerManagement.updateMarker');

        const updatedOrders = data.orders;
        console.log('将更新以下订单的标记 (通用更新):', updatedOrders.map(o => o.id));

        try {
            updatedOrders.forEach(orderToUpdate => {
                if (typeof updateMarker === 'function') {
                     updateMarker(orderToUpdate);
                     console.log(`已请求更新标记 (通用): ${orderToUpdate.id}`);
                } else {
                     console.error("useMarkerManagement 中的 updateMarker 函数不可用！");
                }
            });
        } catch (error) {
            console.error('调用 updateMarker 处理通用更新时出错:', error);
        }
        return; // 处理完毕
    }
    // *** 结束订单更新处理 ***

    // 特殊处理时间范围变化导致的更新
    if (data.isTimeRangeChange) {
        console.log('检测到时间范围变化导致的数据更新，将强制刷新地图')

        // 获取当前的订单数据
        let ordersToDisplay = []

        // 优先使用传入的订单数据
        if (data.orders && Array.isArray(data.orders) && data.orders.length > 0) {
            ordersToDisplay = data.orders
        }
        // 否则使用当前订单列表的缓存
        else if (window.filteredOrdersFromList && Array.isArray(window.filteredOrdersFromList)) {
            ordersToDisplay = window.filteredOrdersFromList
        }
        // 最后使用store中的数据
        else {
            ordersToDisplay = window.showAllOrders ? orderStore.allOrders : orderStore.orders
        }

        // 立即更新地图显示
        if (map.value && isMapLoaded.value) {
            // 清除现有的订单图层数据
            clearOrderSymbols()

            // 清除所有路线图层和源
            console.log('时间范围变化，清空之前的路线')
            clearAllRoutes()

            // 清除路线选择
            if (routeStore.selectedRoute) {
                routeStore.setSelectedRoute(null)
            }

            // 清除司机选择
            if (driverStore.selectedDriver) {
                driverStore.setSelectedDriver(null)
            }

            // 使用小延迟确保清除完成
            setTimeout(() => {
                console.log('时间范围变更，更新订单显示，数量:', ordersToDisplay.length)

                // 使用地理编码处理更新订单
                updateMapWithValidCoordinates(ordersToDisplay || [])

                // 在短暂延迟后绘制新班次的路线
                setTimeout(() => {
                    console.log('绘制新班次的路线')
                    // 检查新班次是否有路线
                    if (routeStore.routes && routeStore.routes.length > 0) {
                        console.log(`新班次有 ${routeStore.routes.length} 条路线，开始绘制`)
                        redrawRoutes()
                    } else {
                        console.log('新班次没有路线，不需要绘制')
                    }
                }, 500) // 给路线数据加载一些时间

                // 调整地图视图
                const validOrders = (ordersToDisplay || []).filter(order => order && (order.lng_lat || order.location))
                if (validOrders.length > 0 && (autoZoom.value || data.isTimeRangeChange)) {
                    console.log(`找到 ${validOrders.length} 个有效订单位置，正在调整地图范围...`)
                    setTimeout(() => {
                        fitMapToOrders(validOrders)
                    }, 100)
                } else if (validOrders.length === 0 && data.isTimeRangeChange) {
                    console.log('没有有效的订单位置坐标，使用默认地图中心点')
                    // 如果没有有效的订单，回到默认位置
                    map.value.flyTo({
                        center: [-79.346574, 43.818219], // 多伦多北约克的坐标
                        zoom: 11,
                        duration: 1000
                    })
                }
            }, 100)
        }
        return;
    }

    // 处理其他普通更新事件
    console.log('处理普通订单更新事件');

    // 更新当前显示的订单
    const ordersToUpdate = window.filteredOrdersFromList ||
                         (window.showAllOrders ? orderStore.allOrders : orderStore.orders);

    if (map.value && isMapLoaded.value) {
        // 更新地图标记
        updateMarkers(ordersToUpdate || []);
    }
}

// 注意：handleOrdersUpdatedFull 函数已被移除，因为它未被使用

// 注意：handleOrderUpdatedIncremental 函数已被移除，因为它未被使用

// **** 新增：处理单个订单添加事件 ****
const handleOrderAddedIncremental = (eventData) => {
    console.log(`MapViewNew 收到单个订单添加事件:`, eventData);

    if (!map.value || !isMapLoaded.value || !eventData || !eventData.order) {
        console.error('无法处理订单添加事件：地图未加载或事件数据无效');
        return;
    }

    const orderData = eventData.order;
    console.log(`处理新添加的订单: ${orderData.id}, 类型: ${orderData.type}`);

    // 检查订单是否符合当前过滤条件
    let shouldDisplay = true;

    // 如果选中了路线，只显示该路线的订单
    if (routeStore.selectedRoute) {
        shouldDisplay = orderData.route_id === routeStore.selectedRoute.id;
    }
    // 如果选中了司机，只显示该司机的订单
    else if (driverStore.selectedDriver) {
        shouldDisplay = orderData.driver_id === driverStore.selectedDriver.id;
    }
    // 否则根据当前显示模式决定
    else {
        shouldDisplay = window.showAllOrders ? true : !orderData.driver_id;
    }

    if (!shouldDisplay) {
        console.log(`新订单 ${orderData.id} 不符合当前地图过滤条件，不添加标记`);
        return;
    }

    // 检查订单是否有有效坐标
    if (!orderData.lng_lat && !orderData.location) {
        console.error(`新订单 ${orderData.id} 没有有效坐标，无法添加标记`);
        return;
    }

    console.log(`新订单 ${orderData.id} 符合显示条件，准备添加标记`);

    // 调用 Marker Manager 添加单个标记
    if (typeof addMarker === 'function') {
        requestAnimationFrame(() => {
            try {
                addMarker(orderData);
                console.log(`成功添加新订单标记: ${orderData.id}`);

                // 检查是否有相同位置的其他订单
                const coords = orderData.lng_lat || orderData.location;
                if (coords && Array.isArray(coords) && coords.length === 2) {
                    const ordersAtLocation = orderStore.allOrders.filter(order => {
                        if (order.id === orderData.id) return false; // 排除自己

                        const orderCoords = order.lng_lat || order.location;
                        if (!orderCoords || !Array.isArray(orderCoords) || orderCoords.length !== 2) return false;

                        // 检查坐标是否非常接近
                        const latDiff = Math.abs(orderCoords[0] - coords[0]);
                        const lngDiff = Math.abs(orderCoords[1] - coords[1]);

                        // 如果坐标差异小于阈值，认为是同一位置
                        return latDiff < 0.0001 && lngDiff < 0.0001;
                    });

                    if (ordersAtLocation.length > 0) {
                        console.log(`新订单 ${orderData.id} 与 ${ordersAtLocation.length} 个现有订单共享相同位置`);

                        // 更新所有共享该位置的订单标记
                        const allOrdersAtLocation = [...ordersAtLocation, orderData];
                        allOrdersAtLocation.forEach(order => {
                            updateMarker(order, true); // 强制更新
                        });
                    }
                }
            } catch (error) {
                console.error(`添加新订单标记失败: ${error.message}`);
            }
        });
    } else {
        console.error("addMarker 函数不可用，无法添加新订单标记");
    }
}

// **** 新增：处理订单取消分配事件 ****
const handleOrdersUnassigned = (eventData) => {
    console.log('🔥 MapViewNew 收到订单取消分配事件:', eventData);
    console.log('🔥 事件数据详情:', JSON.stringify(eventData, null, 2));

    if (!map.value || !isMapLoaded.value || !eventData) {
        console.error('无法处理订单取消分配事件：地图未加载或事件数据无效');
        return;
    }

    const { orders, affectedRouteIds } = eventData;

    if (!orders || !Array.isArray(orders)) {
        console.error('订单取消分配事件数据无效：缺少订单列表');
        return;
    }

    console.log(`处理 ${orders.length} 个取消分配的订单，受影响的路线: ${affectedRouteIds?.join(', ') || '无'}`);

    // 更新地图上的订单标记
    const ordersToUpdate = window.showAllOrders ? orderStore.allOrders : orderStore.orders;
    updateMarkers(ordersToUpdate || []);

    // 重新绘制受影响的路线
    if (affectedRouteIds && affectedRouteIds.length > 0) {
        console.log('重新绘制受影响的路线...');

        // 延迟执行路线重绘，确保 OrderStore 更新完成
        setTimeout(() => {
            affectedRouteIds.forEach(routeId => {
                console.log(`重新绘制路线: ${routeId}`);

                // 获取路线信息
                const routeStoreForRoute = window.routeStore;
                const targetRoute = routeStoreForRoute?.getRouteById ? routeStoreForRoute.getRouteById(routeId) : null;
                const routeDriverId = targetRoute?.driver || targetRoute?.driverId;

                if (routeDriverId && window.mapRouteManagement && window.mapRouteManagement.drawRealisticRoute) {
                    // 获取更新后的订单列表（应该已经移除了取消分配的订单）
                    const updatedRouteOrders = orderStore.getOrdersByRouteNumber(routeId) || [];
                    console.log(`路线 ${routeId} 取消分配后的订单数量: ${updatedRouteOrders.length}`);

                    if (updatedRouteOrders.length > 0) {
                        // 重新绘制路线
                        window.mapRouteManagement.drawRealisticRoute(routeId, routeDriverId, updatedRouteOrders)
                            .then((result) => {
                                if (result) {
                                    console.log(`✅ 路线 ${routeId} 取消分配后重新绘制完成`);
                                } else {
                                    console.warn(`⚠️ 路线 ${routeId} 取消分配后重新绘制返回null`);
                                }
                            })
                            .catch((error) => {
                                console.error(`❌ 路线 ${routeId} 取消分配后重新绘制失败:`, error);
                            });
                    } else {
                        console.log(`路线 ${routeId} 没有剩余订单，隐藏路线`);
                        // 如果路线没有剩余订单，隐藏该路线
                        if (window.mapRouteManagement && window.mapRouteManagement.hideRoute) {
                            window.mapRouteManagement.hideRoute(routeId);
                        }
                    }
                } else {
                    console.warn(`无法重新绘制路线 ${routeId}：缺少司机ID或路线管理功能不可用`);
                }
            });
        }, 500); // 延迟500ms，确保 OrderStore 更新完成
    }
}

// 根据需求将地图缩放适配所有订单
const fitMapToBounds = (orders) => {
    if (!map.value || orders.length === 0) return;

    const validCoordinates = orders
        .map(order => {
            const coords = order.coordinates || (order.location ? [order.location.lng, order.location.lat] : null);
            // 检查坐标是否有效
            if (coords && typeof coords[0] === 'number' && typeof coords[1] === 'number' &&
                coords[0] >= -180 && coords[0] <= 180 && coords[1] >= -90 && coords[1] <= 90) {
                return coords;
            }
            return null;
        })
        .filter(coords => coords !== null);

    if (validCoordinates.length === 0) {
        console.log("没有有效的坐标点来调整边界");
        return;
    }

    console.log(`找到 ${validCoordinates.length} 个有效订单位置，正在调整地图范围...`);

    if (validCoordinates.length === 1) {
        // 只有一个点，飞到该点
        map.value.flyTo({ center: validCoordinates[0], zoom: 14 }); // 可以调整 zoom level
    } else {
        // 计算边界
        const bounds = validCoordinates.reduce((bounds, coord) => {
            return bounds.extend(coord);
        }, new maplibregl.LngLatBounds(validCoordinates[0], validCoordinates[0]));

        console.log("计算的边界范围:", bounds);

        // 调整地图视野以适应边界
        map.value.fitBounds(bounds, {
            padding: { top: 100, bottom: 100, left: 100, right: 100 }, // 增加内边距
            maxZoom: 16, // 限制最大缩放级别
            duration: 1000 // 动画持续时间
        });
    }
};
</script>

<style scoped>
.map-container {
    width: 100%;
    height: 100%;
    position: relative;
    max-height: calc(100vh - var(--g-header-height));
}

/* 路线类型控制样式 */

.route-type-control {
    position: absolute;
    top: 10px;
    left: 10px;
    background: white;
    padding: 5px 10px;
    border-radius: 4px;
    box-shadow: 0 0 10px rgb(0 0 0 / 10%);
    z-index: 1;
    display: flex;
    flex-direction: column;
}

/* 交通图层控制样式 */

.traffic-control {
    position: absolute;
    top: 90px;
    left: 10px;
    background: white;
    padding: 5px 10px;
    border-radius: 4px;
    box-shadow: 0 0 10px rgb(0 0 0 / 10%);
    z-index: 1;
}

/* 性能统计样式 */

.performance-stats {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background: rgb(255 255 255 / 80%);
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 1;
}

/* 选择框样式 */

.selection-box {
    position: absolute;
    border: 2px dashed #1976D2;
    background-color: rgb(25 118 210 / 10%);
    pointer-events: none;
    z-index: 2;
}

/* 选择提示样式 */

.selection-tooltip {
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgb(0 0 0 / 70%);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 14px;
    z-index: 10;
    pointer-events: none;
    transition: opacity 0.3s ease;
}

/* 自动缩放控制样式 */

.zoom-control {
    position: absolute;
    top: 130px;
    left: 10px;
    background: white;
    padding: 5px 10px;
    border-radius: 4px;
    box-shadow: 0 0 10px rgb(0 0 0 / 10%);
    z-index: 1;
}

/* 司机位置显示控制样式 */

.driver-control {
    position: absolute;
    top: 170px;
    left: 10px;
    background: white;
    padding: 5px 10px;
    border-radius: 4px;
    box-shadow: 0 0 10px rgb(0 0 0 / 10%);
    z-index: 1;
}

/* 幽灵标记样式 */
:deep(.ghost-marker) {
    width: 50px;
    height: 50px;
    background-color: rgba(0, 123, 255, 0.8);
    border: 2px solid white;
    border-radius: 50%;
    color: white;
    font-weight: bold;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
    pointer-events: none; /* 确保不会干扰鼠标事件 */
    animation: pulse 1.5s infinite; /* 添加脉冲动画 */
    z-index: 1000; /* 确保在最上层 */
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.1);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 0.8;
    }
}

:deep(.ghost-marker.multi-drag) {
    background-color: rgba(220, 53, 69, 0.8);
}

:deep(.ghost-marker.same-location) {
    background-color: rgba(255, 193, 7, 0.8); /* 黄色 */
    border: 2px dashed #fff;
}

/* 地图图例样式 */
.map-legend {
    position: absolute;
    bottom: 20px;
    left: 10px;
    background: white;
    padding: 10px;
    border-radius: 4px;
    box-shadow: 0 0 10px rgb(0 0 0 / 10%);
    z-index: 1;
}

.legend-title {
    font-weight: bold;
    margin-bottom: 5px;
}

.legend-item {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    margin-right: 8px;
}

/* 添加保存路线按钮样式 */
.save-route-button {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 100;
    background-color: rgba(255, 255, 255, 0.9);
    padding: 5px;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>

<style>
/* 全局弹窗样式 */

.marker-hover-popup {
    z-index: 2000 !important;
    transition: opacity 0.2s ease-in-out;
}

.marker-hover-popup .maplibregl-popup-content {
    background: white;
    padding: 10px;
    border-radius: 8px;
    box-shadow: 0 3px 10px rgb(0 0 0 / 20%);
    overflow-y: auto;
    max-height: 180px !important; /* 限制最大高度 */
}

.marker-hover-popup .maplibregl-popup-tip {
    border-top-color: white !important;
}

/* 根据不同的锚点位置调整箭头颜色 */

.marker-hover-popup.maplibregl-popup-anchor-top .maplibregl-popup-tip {
    border-bottom-color: white !important;
    border-top-color: transparent !important;
}

.marker-hover-popup.maplibregl-popup-anchor-bottom .maplibregl-popup-tip {
    border-top-color: white !important;
    border-bottom-color: transparent !important;
}

.marker-hover-popup.maplibregl-popup-anchor-left .maplibregl-popup-tip {
    border-right-color: white !important;
    border-left-color: transparent !important;
}

.marker-hover-popup.maplibregl-popup-anchor-right .maplibregl-popup-tip {
    border-left-color: white !important;
    border-right-color: transparent !important;
}

/* 滚动条美化 */

.marker-hover-popup .maplibregl-popup-content::-webkit-scrollbar {
    width: 4px;
}

.marker-hover-popup .maplibregl-popup-content::-webkit-scrollbar-track {
    background: #f5f5f5;
    border-radius: 3px;
}

.marker-hover-popup .maplibregl-popup-content::-webkit-scrollbar-thumb {
    background: #ddd;
    border-radius: 3px;
}

.marker-hover-popup .maplibregl-popup-content::-webkit-scrollbar-thumb:hover {
    background: #ccc;
}

/* 司机家庭地址控制样式 */
.driver-home-control {
    position: absolute;
    top: 90px;
    right: 10px;
    background: white;
    padding: 5px 10px;
    border-radius: 4px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    z-index: 1;
    font-size: 14px;
}


</style>